import 'package:flutter/material.dart';
import 'package:nsl/l10n/app_localizations.dart';
import 'package:nsl/providers/create_entity_provider.dart';
import 'package:nsl/providers/manual_creation_provider.dart';
import 'package:nsl/providers/web_home_provider_static.dart';
import 'package:nsl/providers/object_creation_provider.dart';
import 'package:nsl/models/object_creation_model.dart';
import 'package:nsl/screens/web/static_flow/customer_onboarding/widgets/custom_dropdown_widget.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/utils/font_manager.dart';
import 'package:nsl/utils/responsive_font_sizes.dart';
import 'dart:math' as math;
import 'package:provider/provider.dart';

/// Helper class for column width constraints
class ColumnConstraints {
  final double minWidth;
  final double maxWidth;

  const ColumnConstraints({
    required this.minWidth,
    required this.maxWidth,
  });
}

class InnerEntityScreen extends StatefulWidget {
  const InnerEntityScreen({super.key});

  @override
  State<InnerEntityScreen> createState() => _InnerEntityScreenState();
}

class _InnerEntityScreenState extends State<InnerEntityScreen> {
  final FocusNode entityNameFocusNode = FocusNode();

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      final entityProvider =
          Provider.of<CreateEntityProvider>(context, listen: false);
      entityProvider.setupScrollSync();
      if (entityNameFocusNode.canRequestFocus) {
        FocusScope.of(context).requestFocus(entityNameFocusNode);
      }
    });
  }

  @override
  void dispose() {
    final entityProvider =
        Provider.of<CreateEntityProvider>(context, listen: false);

    entityProvider.headerScrollController.dispose();
    for (final relationship
        in entityProvider.currentEntity?.relationships ?? []) {
      relationship.scrollController.dispose();
    }
    entityNameFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer2<WebHomeProviderStatic, CreateEntityProvider>(
      builder: (context, provider, entityProvider, child) {
        return Container(
          width: double.infinity,
          height: double.infinity,
          color: Colors.white,
          child: Column(
            children: [
              // Header with toggle
              // _buildHeader(context, provider),

              // Content area
              Expanded(
                child: _buildContent(context, entityProvider),
              ),
              if ((entityProvider
                          .currentEntity?.isEntityAttributesValidatedSaved ??
                      false)
                  //      &&
                  // (entityProvider
                  //         .currentEntity?.isEntityRelationshipsValidatedSaved ??
                  //     false) &&
                  // (entityProvider
                  //         .currentEntity?.isEntityEnumValuesValidatedSaved ??
                  //     false)
                  )
                Container(
                  width: double.infinity,
                  decoration: BoxDecoration(color: Colors.white, boxShadow: [
                    BoxShadow(
                      color: Color(0xfff0f0f0),
                      offset: Offset(0, -3),
                      blurRadius: 5,
                    )
                  ]),
                  padding: const EdgeInsets.all(AppSpacing.sm),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      ElevatedButton(
                        onPressed: () async {
                          if (await Provider.of<ObjectCreationProvider>(context,
                                  listen: false)
                              .publish(entityProvider.currentEntity)) {
                            await showDialog(
                              context: context,
                              barrierDismissible: true,
                              builder: (BuildContext context) {
                                return AlertDialog(
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  title: Text(
                                    'Success',
                                    style: FontManager.getCustomStyle(
                                      fontSize: ResponsiveFontSizes.titleMedium(
                                          context),
                                      fontWeight: FontWeight.w600,
                                      fontFamily:
                                          FontManager.fontFamilyTiemposText,
                                      color: Colors.black,
                                    ),
                                  ),
                                  content: Text(
                                    'Published Entity successfully.',
                                    style: FontManager.getCustomStyle(
                                      fontSize: ResponsiveFontSizes.bodyMedium(
                                          context),
                                      fontWeight: FontWeight.w400,
                                      fontFamily:
                                          FontManager.fontFamilyTiemposText,
                                      color: Colors.grey[700],
                                    ),
                                  ),
                                  actions: [
                                    ElevatedButton(
                                      onPressed: () {
                                        entityProvider.resetObject();
                                        Navigator.of(context).pop();
                                      },
                                      style: ElevatedButton.styleFrom(
                                        backgroundColor:
                                            const Color(0xFF0058FF),
                                        foregroundColor: Colors.white,
                                        shape: RoundedRectangleBorder(
                                          borderRadius:
                                              BorderRadius.circular(8),
                                        ),
                                      ),
                                      child: Text(
                                        'Ok',
                                        style: FontManager.getCustomStyle(
                                          fontSize:
                                              ResponsiveFontSizes.bodyMedium(
                                                  context),
                                          fontWeight: FontWeight.w500,
                                          fontFamily:
                                              FontManager.fontFamilyTiemposText,
                                          color: Colors.white,
                                        ),
                                      ),
                                    ),
                                  ],
                                );
                              },
                            );
                          }
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFF0058FF),
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(
                              horizontal: 24, vertical: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(2),
                          ),
                          elevation: 0,
                        ),
                        child: Text(
                          'Publish',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.bodyMedium(context),
                            fontWeight: FontWeight.w500,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ],
                  ),
                )
            ],
          ),
        );
      },
    );
  }

  Widget _buildHeader(BuildContext context, WebHomeProviderStatic provider) {
    return Container(
      padding: const EdgeInsets.all(10),
      decoration: const BoxDecoration(
        color: Colors.black,
        border: Border(
          bottom: BorderSide(color: Color(0xFFE5E7EB), width: 1),
        ),
        boxShadow: [
          BoxShadow(
            color: Color(0x1A000000), // Black with 10% opacity
            blurRadius: 8,
            offset: Offset(0, 2),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Dynamic header label based on toggle state
          Text(
            // provider.isAIMode ? 'Objects' : 'Extracted Details',
            'Extracted Details',
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.titleSmall(context),
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.white,
              fontWeight: FontWeight.bold,
              height: 1,
            ),
          ),

          // Right side with toggle and manually process text
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              // AI/Manual Toggle
              _buildAIManualToggle(context, provider),

              const SizedBox(width: 16),

              // // Manually Process text
              // Text(
              //   'Manually Process',
              //   style: FontManager.getCustomStyle(
              //     fontSize: ResponsiveFontSizes.titleSmall(context),
              //     color: Colors.white,
              //     fontWeight: FontWeight.w400,
              //     fontFamily: FontManager.fontFamilyTiemposText,
              //     height: 1,
              //   ),
              // ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildAIManualToggle(
      BuildContext context, WebHomeProviderStatic provider) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        // Toggle switch
        MouseRegion(
          cursor: SystemMouseCursors.click,
          child: InkWell(
            onTap: () {
              // Close entity details panel if it's open before switching modes
              final manualProvider =
                  Provider.of<ManualCreationProvider>(context, listen: false);
              if (manualProvider.selectedEntity != null) {
                manualProvider.setSelectedEntity(null);
              }

              provider.toggleAIMode();
              manualProvider.handleEntityValidationForBook();
            },
            child: Container(
              width: 34,
              height: 18,
              decoration: BoxDecoration(
                color: Colors.white,
                border: Border.all(
                  color: Colors.black,
                  width: 1,
                ),
                borderRadius: BorderRadius.circular(10),
              ),
              child: AnimatedAlign(
                duration: const Duration(milliseconds: 200),
                curve: Curves.easeInOut,
                alignment: provider.isAIMode
                    ? Alignment.centerLeft
                    : Alignment.centerRight,
                child: Container(
                  width: 16,
                  height: 16,
                  decoration: const BoxDecoration(
                    color: Color(0xFF0058FF),
                    shape: BoxShape.circle,
                  ),
                ),
              ),
            ),
          ),
        ),

        const SizedBox(width: 4),

        // ai label on the right
        Text(
          'Form',
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.titleSmall(context),
            color: Colors.white,
            fontWeight: FontWeight.w400,
            fontFamily: FontManager.fontFamilyTiemposText,
            height: 1,
          ),
        ),
        const SizedBox(width: 12),
        // manua; label on the left
        Text(
          'Manual Process',
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.titleSmall(context),
            color: Colors.white,
            fontWeight: FontWeight.w400,
            fontFamily: FontManager.fontFamilyTiemposText,
            height: 1,
          ),
        ),
      ],
    );
  }

  Widget _buildContent(
      BuildContext context, CreateEntityProvider entityProvider) {
    return _buildContentWithLineNumbers(context, entityProvider);
  }

  Widget _buildContentWithLineNumbers(
      BuildContext context, CreateEntityProvider entityProvider) {
    int lineNumber = 1;

    return SingleChildScrollView(
      child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
        const SizedBox(
          height: AppSpacing.xs,
        ),
        if (entityProvider.isEditMode) ...[
          _buildLineWithNumber(
              lineNumber++, _buildFirstRow(context, entityProvider),
              requirePadding: true),
          _buildLineWithNumber(
              lineNumber++, _buildDescriptionRow(context, entityProvider),
              requirePadding: true),
        ],
        if (!entityProvider.isEditMode) ...[
          _buildLineWithNumber(
              lineNumber++, _buildEntityFirstRow(context, entityProvider),
              requirePadding: true),
        ],
        //attributes
        if (entityProvider.currentEntity != null &&
            entityProvider.currentEntity?.attributes != null) ...[
          _buildLineWithNumber(
            lineNumber++,
            expansionItemWidget(
                context,
                "Attributes Details",
                "${entityProvider.currentEntity?.attributes?.length} Attributes",
                entityProvider.currentEntity?.attributesExpanded ?? false,
                () {
                  entityProvider.updateEntityAttributeExpansion(
                      entityProvider.currentEntity?.attributesExpanded);
                },
                "Add Attribute",
                () {
                  _showAttributeDialog(
                      context: context,
                      title: "Attribute Configuration",
                      entityProvider: entityProvider);
                },
                isValidated: entityProvider
                        .currentEntity?.isEntityAttributesValidatedSaved ??
                    false),
            requirePadding:
                !(entityProvider.currentEntity?.attributesExpanded ?? false),
          ),
          if (entityProvider.currentEntity?.attributesExpanded ?? false) ...[
            _buildLineWithNumber(
              lineNumber++,
              _buildTableHeaders([
                "NAME",
                "DISPLAY NAME",
                "DATA TYPE",
                "REQUIRED",
                "UNIQUE",
                // "KEY",
                "ACTIONS",
              ]),
            ),
            ...entityProvider.currentEntity!.attributes!
                .asMap()
                .entries
                .map((e) {
              final att = e.value;

              final nameController = TextEditingController(text: att.name);
              final displayNameController =
                  TextEditingController(text: att.displayName);

              String attDataType = att.dataType ?? '';
              String attRequired = att.required == true ? "Yes" : "No";
              String attUnique = att.unique == true ? "Yes" : "No";

              return _buildLineWithNumber(
                lineNumber++,
                StatefulBuilder(builder: (context, setState) {
                  return EditableRow(
                    tableHeaders: [
                      att.name ?? '',
                      att.displayName ?? '',
                      att.dataType ?? '',
                      att.required == true ? "Yes" : "No",
                      att.unique == true ? "Yes" : "No",
                      "DELETE"
                    ],
                    onDelete: () {
                      entityProvider.currentEntity!.attributes!.remove(att);
                      entityProvider.update();
                    },
                    editWidgets: [
                      customTextField(nameController),
                      customTextField(displayNameController),
                      CustomDropdownWidget(
                        label: "Data Type",
                        list: entityProvider.attributesDataTypes,
                        onChanged: (value) {
                          setState(() {
                            attDataType = value;
                          });
                        },
                        value: attDataType,
                      ),
                      CustomDropdownWidget(
                        label: "Required",
                        list: entityProvider.attributesRequired,
                        onChanged: (value) {
                          setState(() {
                            attRequired = value;
                          });
                        },
                        value: attRequired,
                      ),
                      CustomDropdownWidget(
                        label: "Unique",
                        list: entityProvider.attributesUnique,
                        onChanged: (value) {
                          setState(() {
                            attUnique = value;
                          });
                        },
                        value: attUnique,
                      ),
                    ],
                    onUpdate: () async {
                      final value = await _validateEntity(
                        context,
                        entityProvider.currentEntity!,
                        "Attribute Details",
                      );
                      if (value) {
                        entityProvider.currentEntity!.attributes![entityProvider
                            .currentEntity!.attributes!
                            .indexOf(e.value)] = ObjectAttribute(
                          name: nameController.text,
                          displayName: displayNameController.text,
                          dataType: attDataType,
                          required: attRequired == "Yes" ? true : false,
                          unique: attUnique == "Yes" ? true : false,
                        );
                      }
                    },
                  );
                }),
              );
            })
          ],
          //relationships
          if (entityProvider.currentEntity != null &&
              entityProvider.currentEntity?.relationships != null) ...[
            _buildLineWithNumber(
                lineNumber++,
                expansionItemWidget(
                    context,
                    "Entity Relationship",
                    "${entityProvider.currentEntity?.relationships?.length} rules Configured",
                    entityProvider.currentEntity?.relationshipsExpanded ??
                        false,
                    () {
                      entityProvider.updateEntityRelationShipExpansion(
                          entityProvider.currentEntity?.relationshipsExpanded);
                    },
                    "Add Relationship",
                    () {
                      _showRelationshipsDialog(
                          context: context,
                          title: "Add Entity Relationship",
                          entityProvider: entityProvider);
                    },
                    isValidated: entityProvider.currentEntity
                            ?.isEntityRelationshipsValidatedSaved ??
                        false),
                requirePadding:
                    !(entityProvider.currentEntity?.relationshipsExpanded ??
                        false)),
            if (entityProvider.currentEntity?.relationshipsExpanded ??
                false) ...[
              _buildLineWithNumber(
                lineNumber++,
                _buildTableHeaders([
                  "PRIMARY KEY",
                  "FORIGN KEY",
                  "RELATIONSHIP TYPE",
                  "ON DELETE",
                  "FOREIGN KEY TYPE",
                  "ON UPDATE",
                  "DESCRIPTION",
                  "ACTIONS"
                ], scrollController: entityProvider.headerScrollController),
              ),
              ...entityProvider.currentEntity!.relationships!
                  .asMap()
                  .entries
                  .map((e) {
                final rel = e.value;

                TextEditingController descriptionController =
                    TextEditingController(text: rel.description);
                String? primaryKey = rel.primaryKey;
                String? foreignKey = rel.foreignKey;
                String? relationshipType = rel.relationshipType;
                String? onDelete = rel.onDelete;
                String? onUpdate = rel.onUpdate;
                String? foreignKeyType = rel.foreignKeyType;

                return _buildLineWithNumber(
                  lineNumber++,
                  StatefulBuilder(builder: (context, setState) {
                    return EditableRow(
                      tableHeaders: [
                        rel.primaryKey ?? '',
                        rel.foreignKey ?? '',
                        rel.relationshipType ?? '',
                        rel.onDelete ?? '',
                        rel.foreignKeyType ?? '',
                        rel.onUpdate ?? '',
                        rel.description ?? '',
                        "DELETE"
                      ],
                      onDelete: () {
                        entityProvider.currentEntity!.relationships!
                            .remove(rel);
                        entityProvider.update();
                      },
                      editWidgets: [
                        CustomDropdownWidget(
                            label: "Primary Key",
                            onChanged: (value) {
                              primaryKey = value;
                              setState(() {});
                            },
                            list: entityProvider.currentEntity?.attributes
                                    ?.map(
                                      (e) => e.name,
                                    )
                                    .toList() ??
                                [],
                            value: primaryKey),
                        CustomDropdownWidget(
                            label: "Foreign Key",
                            onChanged: (value) {
                              foreignKey = value;
                              setState(() {});
                            },
                            list: entityProvider.currentEntity?.attributes
                                    ?.map(
                                      (e) => e.name,
                                    )
                                    .toList() ??
                                [],
                            value: foreignKey),
                        CustomDropdownWidget(
                            label: "Relationship Type",
                            onChanged: (value) {
                              relationshipType = value;
                              setState(() {});
                            },
                            list: entityProvider.relationshipTypes,
                            value: relationshipType),
                        CustomDropdownWidget(
                            label: "On Delete",
                            onChanged: (value) {
                              onDelete = value;
                              setState(() {});
                            },
                            list: entityProvider.onDeletes,
                            value: onDelete),
                        CustomDropdownWidget(
                            label: "On Update",
                            onChanged: (value) {
                              onUpdate = value;
                              setState(() {});
                            },
                            list: entityProvider.onUpdates,
                            value: onUpdate),
                        CustomDropdownWidget(
                            label: "Foreign Key Type",
                            onChanged: (value) {
                              foreignKeyType = value;
                              setState(() {});
                            },
                            list: entityProvider.foreignKeyTypes,
                            value: foreignKeyType),
                        customTextField(
                          descriptionController,
                        )
                      ],
                      onUpdate: () async {
                        final value = await _validateEntity(
                          context,
                          entityProvider.currentEntity!,
                          "Entity Relationships",
                        );
                        if (value) {
                          entityProvider.currentEntity!.relationships![
                              entityProvider.currentEntity!.relationships!
                                  .indexOf(e.value)] = EntityRelationship(
                              primaryKey: primaryKey,
                              foreignKey: foreignKey,
                              relationshipType: relationshipType,
                              onDelete: onDelete,
                              onUpdate: onUpdate,
                              foreignKeyType: foreignKeyType,
                              description: descriptionController.text);
                        }
                      },
                      scrollController: rel.scrollController,
                    );
                  }),
                );
              })
            ]
          ],

          //enum
          if (entityProvider.currentEntity != null &&
              entityProvider.currentEntity?.enumValues != null) ...[
            _buildLineWithNumber(
                lineNumber++,
                expansionItemWidget(
                    context,
                    "Enumerated Values",
                    "${entityProvider.currentEntity?.enumValues?.length} Configured",
                    entityProvider.currentEntity?.enumValuesExpanded ?? false,
                    () {
                      entityProvider.updateEntityEnumExpansion(
                          entityProvider.currentEntity?.enumValuesExpanded);
                    },
                    "Add Enum",
                    () {
                      _showEnumDialog(
                          context: context,
                          title: "Create Enumerated Values",
                          entityProvider: entityProvider);
                    },
                    isValidated: entityProvider
                            .currentEntity?.isEntityEnumValuesValidatedSaved ??
                        false),
                requirePadding:
                    !(entityProvider.currentEntity?.enumValuesExpanded ??
                        false)),
            if (entityProvider.currentEntity?.enumValuesExpanded ?? false) ...[
              _buildLineWithNumber(
                lineNumber++,
                _buildTableHeaders([
                  "ATTRIBUTE",
                  // "USER",
                  "VALUE", "ACTIONS"
                ]),
              ),
              ...entityProvider.currentEntity!.enumValues!
                  .asMap()
                  .entries
                  .map((e) {
                final enu = e.value;
                String? attribute = enu.entityAttribute;
                TextEditingController textController =
                    TextEditingController(text: enu.value);

                return _buildLineWithNumber(
                  lineNumber++,
                  StatefulBuilder(builder: (context, setState) {
                    return EditableRow(
                        tableHeaders: [
                          enu.entityAttribute ?? '',
                          // e.value.enumName ?? '',
                          enu.value ?? '',
                          "DELETE"
                        ],
                        onDelete: () {
                          entityProvider.currentEntity!.enumValues!.remove(enu);
                          entityProvider.update();
                        },
                        editWidgets: [
                          CustomDropdownWidget(
                              label: "Attribute",
                              onChanged: (value) {
                                attribute = value;
                                setState(() {});
                              },
                              list: entityProvider.currentEntity?.attributes
                                      ?.map(
                                        (e) => e.name,
                                      )
                                      .toList() ??
                                  [],
                              value: attribute),
                          customTextField(
                            textController,
                          )
                        ],
                        onUpdate: () async {
                          final value = await _validateEntity(
                            context,
                            entityProvider.currentEntity!,
                            "Enumerated Values",
                          );
                          if (value) {
                            entityProvider.currentEntity!.enumValues![
                                entityProvider
                                    .currentEntity!.enumValues!
                                    .indexOf(e.value)] = EnumValue(
                                entityAttribute: attribute,
                                value: textController.text);
                          }
                        });
                  }),
                );
              })
            ]
          ],
        ],
      ]),
    );
  }

  Widget _buildFirstRow(
      BuildContext context, CreateEntityProvider entityProvider) {
    return Row(
      children: [
        // Solution label and text field
        Expanded(
          flex: 3,
          child: Row(
            children: [
              Text(
                'Object:',
                style: FontManager.getCustomStyle(
                    fontSize: FontManager.s12,
                    fontWeight: FontWeight.w700,
                    color: Colors.black,
                    fontFamily: FontManager.fontFamilyTiemposText),
              ),
              const SizedBox(width: AppSpacing.xs),
              Expanded(
                child: customTextField(entityProvider.entityNameController,
                    focusNode: entityNameFocusNode),
              ),
            ],
          ),
        ),
        const SizedBox(width: AppSpacing.xs),
        // Entity Type dropdown
        Expanded(
          flex: 1,
          child: CustomDropdownWidget(
            label: 'Select Type',
            list: CreateEntityProvider.entityTypes,
            value: entityProvider.selectedEntityType,
            onChanged: (value) {
              entityProvider.updateEntityValue(value);
            },
            customLabelWidget:
                getTypeForSelectedEntity(entityProvider.selectedEntityType),
          ),
        ),
        const SizedBox(width: AppSpacing.xs),
        Expanded(
          flex: 1,
          child: Row(
            children: [
              Text(
                'Icon 16x16:',
                style: FontManager.getCustomStyle(
                    fontSize: FontManager.s12,
                    fontWeight: FontWeight.w700,
                    color: Colors.black,
                    fontFamily: FontManager.fontFamilyTiemposText),
              ),
              const SizedBox(width: AppSpacing.xs),
              Expanded(
                child: customTextField(entityProvider.entityIconController),
              ),
            ],
          ),
        ),
        const SizedBox(width: AppSpacing.xs),
        // Tick icon button
        InkWell(
          onTap: entityProvider.isValidating
              ? null
              : () async {
                  final errorMessage = await entityProvider.validateSolution(
                      validate: await _validateEntity(
                    context,
                    ObjectCreationModel(
                      name: entityProvider.entityNameController.text,
                      displayName: entityProvider.entityNameController.text,
                      icon: entityProvider.entityIconController.text,
                      type: entityProvider.selectedEntityType,
                      description:
                          entityProvider.entityDescriptionController.text,
                    ),
                    "Object Details",
                  ));
                  if (errorMessage != null && errorMessage.isNotEmpty) {
                    await showDialog(
                      context: context,
                      builder: (context) {
                        return validationErrorDialog(
                            context, "Creating Entity error", errorMessage);
                      },
                    );
                  }
                },
          child: Container(
            width: 24,
            height: 24,
            decoration: BoxDecoration(
              color: entityProvider.isValidating
                  ? Color(0xfff0f0f0)
                  : const Color(0xFF007AFF),
              shape: BoxShape.circle,
            ),
            child: const Icon(
              Icons.check,
              color: Colors.white,
              size: 14,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDescriptionRow(context, CreateEntityProvider entityProvider) {
    return Row(
      children: [
        Expanded(
          flex: 3,
          child: Row(
            children: [
              Text(
                'Description:',
                style: FontManager.getCustomStyle(
                    fontSize: FontManager.s12,
                    fontWeight: FontWeight.w700,
                    color: Colors.black,
                    fontFamily: FontManager.fontFamilyTiemposText),
              ),
              const SizedBox(width: AppSpacing.xs),
              Expanded(
                child:
                    customTextField(entityProvider.entityDescriptionController),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildEntityFirstRow(
      BuildContext context, CreateEntityProvider entityProvider) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        MouseRegion(
          cursor: SystemMouseCursors.click,
          child: InkWell(
            onTap: () {
              entityProvider.updateEditMode(true);
            },
            child: Text(
              'Object: ${entityProvider.currentEntity?.displayName ?? ''}',
              style: FontManager.getCustomStyle(
                  fontSize: FontManager.s12,
                  fontWeight: FontWeight.w700,
                  color: Colors.black,
                  fontFamily: FontManager.fontFamilyTiemposText),
            ),
          ),
        ),
        CustomDropdownWidget(
          label: 'Select Type',
          list: CreateEntityProvider.entityTypes,
          value: entityProvider.selectedEntityType,
          onChanged: (value) {
            entityProvider.updateEntityValue(value);
          },
          customLabelWidget:
              getTypeForSelectedEntity(entityProvider.selectedEntityType),
        ),
      ],
    );
  }

  // Helper methods for line numbers
  Widget _buildLineWithNumber(int lineNumber, Widget content,
      {bool requirePadding = false}) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        // Line number
        Container(
          width: 40,
          // color: Color(0xFFEDF3FF),
          decoration: BoxDecoration(
            color: Colors.white,
            // border: Border.all(color: Color(0xffD0D0D0))
          ),
          padding: EdgeInsets.all(AppSpacing.xs),
          child: Text(
            '$lineNumber',
            textAlign: TextAlign.center,
            style: FontManager.getCustomStyle(
              fontSize: FontManager.s10,
              fontWeight: FontWeight.w400,
              color: Colors.black,
              fontFamily: FontManager.fontFamilyTiemposText,
            ),
          ),
        ),
        // Content
        Expanded(
            child: Padding(
          padding: EdgeInsets.fromLTRB(
            AppSpacing.xs,
            requirePadding ? AppSpacing.xxs : 0,
            AppSpacing.sm,
            requirePadding ? AppSpacing.xxs : 0,
          ),
          child: content,
        )),
      ],
    );
  }

  Widget customTextField(TextEditingController controller,
      {FocusNode? focusNode}) {
    return Container(
        height: 35,
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey.shade300),
          borderRadius: BorderRadius.circular(2),
        ),
        child: Center(
          child: TextField(
            focusNode: focusNode,
            controller: controller,
            decoration: const InputDecoration(
              border: InputBorder.none,
              enabledBorder: InputBorder.none,
              focusedBorder: InputBorder.none,
              fillColor: Colors.transparent,
              hoverColor: Colors.transparent,
              contentPadding: EdgeInsets.symmetric(
                  horizontal: AppSpacing.xs, vertical: AppSpacing.xs),
              isDense: true,
            ),
            style: FontManager.getCustomStyle(
                fontSize: FontManager.s10,
                fontWeight: FontWeight.w500,
                color: Colors.black,
                fontFamily: FontManager.fontFamilyTiemposText),
          ),
        ));
  }

  Widget getTypeForSelectedEntity(String value) {
    Color color = Colors.white;
    switch (value) {
      case "Master":
        color = Color(0xff9dc3ff);
      case "Reference":
        color = Color(0xffFF9F68);
      case "Transaction":
        color = Color(0xffA3E4D7);
      case "Configuration":
        color = Color(0xffFAD02E);
      case "Intelligence":
        color = Color(0xffD7BDE2);
      case "Aggregate":
        color = Color(0xffF5B7B1);
      case "Contextual":
        color = Color(0xffF8C471);
    }
    return Container(
      padding: EdgeInsets.all(4),
      margin: EdgeInsets.all(4),
      decoration: BoxDecoration(color: color
          //  Color(0xff9dc3ff)
          ),
      child: Text(
        value.capitalize(),
        style: FontManager.getCustomStyle(
          fontFamily: FontManager.fontFamilyTiemposText,
          // fontWeight: FontWeight.w500
        ),
      ),
    );
  }

  Widget expansionItemWidget(
      BuildContext context,
      String title,
      String displayCount,
      bool isExpanded,
      VoidCallback onTap,
      String buttonText,
      VoidCallback buttonOnTap,
      {bool isValidated = false,
      bool isExpandEnabled = true}) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 2),
      decoration: BoxDecoration(
        border: Border.all(
          color: isExpanded ? const Color(0xFF0058FF) : const Color(0xFFE5E7EB),
          width: isExpanded ? 2 : 1,
        ),
        borderRadius: BorderRadius.circular(4),
        color: Colors.white,
      ),
      child: Column(
        children: [
          InkWell(
            onTap: isExpandEnabled ? onTap : null,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              child: Row(
                children: [
                  Expanded(
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        // Title Text - Dynamic width
                        Flexible(
                          flex: 3,
                          child: Row(
                            children: [
                              Text(
                                title,
                                style: FontManager.getCustomStyle(
                                  fontSize:
                                      ResponsiveFontSizes.bodyMedium(context),
                                  fontWeight: isExpanded
                                      ? FontWeight.w500
                                      : FontWeight.w300,
                                  fontFamily: FontManager.fontFamilyTiemposText,
                                  color: Colors.black,
                                ),
                                overflow: TextOverflow.visible,
                                maxLines: 2,
                              ),
                              if (isValidated)
                                Visibility.maintain(
                                  visible: isValidated,
                                  child: Padding(
                                    padding: const EdgeInsets.only(right: 8.0),
                                    child: Icon(
                                      Icons.check,
                                      color: Colors.green,
                                    ),
                                  ),
                                ),
                            ],
                          ),
                        ),
                        const SizedBox(width: 8),
                        // Status Badge - Dynamic width
                        // if (displayStatus.isNotEmpty)
                        //   Flexible(
                        //     flex: 2,
                        //     child: Container(
                        //       padding: const EdgeInsets.symmetric(
                        //           horizontal: 8, vertical: 4),
                        //       decoration: BoxDecoration(
                        //         color: displayBackgroundColor,
                        //         borderRadius: BorderRadius.circular(4),
                        //       ),
                        //       child: Text(
                        //         displayStatus,
                        //         style: FontManager.getCustomStyle(
                        //           fontSize:
                        //               ResponsiveFontSizes.labelSmall(context),
                        //           fontWeight: FontWeight.w500,
                        //           fontFamily: FontManager.fontFamilyTiemposText,
                        //           color: displayTextColor,
                        //         ),
                        //         overflow: TextOverflow.visible,
                        //         textAlign: TextAlign.center,
                        //       ),
                        //     ),
                        //   ),
                      ],
                    ),
                  ),
                  const SizedBox(width: 12),
                  ElevatedButton.icon(
                    onPressed: buttonOnTap,
                    icon: const Icon(
                      Icons.add,
                      size: 16,
                      color: Colors.white,
                    ),
                    label: Text(buttonText),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF0058FF),
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 6),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(6),
                      ),
                      elevation: 0,
                      textStyle: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.labelSmall(context),
                        fontWeight: FontWeight.w500,
                        fontFamily: FontManager.fontFamilyTiemposText,
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Container(
                    width: 100,
                    alignment: Alignment.centerRight,
                    child: Text(
                      displayCount,
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.labelSmall(context),
                        fontWeight: FontWeight.w500,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.black,
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  AnimatedRotation(
                    turns: isExpanded ? 0.5 : 0.0,
                    duration: const Duration(milliseconds: 200),
                    child: Icon(
                      Icons.keyboard_arrow_down,
                      color: Colors.grey[600],
                      size: 16,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget validationErrorDialog(
      BuildContext context, String title, String errorString) {
    final provider =
        Provider.of<ObjectCreationProvider>(context, listen: false);

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Container(
        width: math.min(MediaQuery.of(context).size.width * 0.8, 534),
        height: math.min(MediaQuery.of(context).size.height * 0.8, 600),
        child: Column(
          children: [
            // Header with bottom border
            Container(
              padding: const EdgeInsets.only(
                  bottom: 14, top: 14, left: 24, right: 24),
              decoration: const BoxDecoration(
                border: Border(
                  bottom: BorderSide(color: Color(0xFFE5E7EB), width: 1),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    '$title Validation Error',
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.bodyLarge(context),
                      fontWeight: FontWeight.w600,
                      fontFamily: FontManager.fontFamilyInter,
                      color: Colors.black,
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close),
                    iconSize: 24,
                    color: Colors.grey[600],
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                  ),
                ],
              ),
            ),
            Expanded(
                child: Padding(
              padding:
                  const EdgeInsets.symmetric(horizontal: 24, vertical: 8.0),
              child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Purpose',
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.bodyLarge(context),
                        fontWeight: FontWeight.w500,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.black,
                      ),
                    ),
                    const SizedBox(
                      height: AppSpacing.xs,
                    ),
                    Text(
                      "We encountered one or more issues. Please review the following:",
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.titleSmall(context),
                        fontWeight: FontWeight.w400,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.black,
                      ),
                    ),
                    const SizedBox(
                      height: AppSpacing.xs,
                    ),
                    Expanded(
                      child: Container(
                        width: double.infinity,
                        decoration: BoxDecoration(
                          color: Color(0xffF8FAFC),
                          border: Border.all(color: Color(0xffE5E7EA)),
                          borderRadius: BorderRadius.circular(10),
                        ),
                        padding: EdgeInsets.all(AppSpacing.sm),
                        child: SingleChildScrollView(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            spacing: AppSpacing.xs,
                            children: [
                              if (errorString.isNotEmpty)
                                Text(
                                  errorString,
                                  style: FontManager.getCustomStyle(
                                    fontSize:
                                        ResponsiveFontSizes.titleSmall(context),
                                    fontWeight: FontWeight.w400,
                                    fontFamily:
                                        FontManager.fontFamilyTiemposText,
                                    color: Colors.black,
                                  ),
                                ),
                              if (provider.parseValidationEntityModel?.error !=
                                  null)
                                Text(
                                  provider.parseValidationEntityModel!.error!,
                                  style: FontManager.getCustomStyle(
                                    fontSize:
                                        ResponsiveFontSizes.titleSmall(context),
                                    fontWeight: FontWeight.w400,
                                    fontFamily:
                                        FontManager.fontFamilyTiemposText,
                                    color: Colors.black,
                                  ),
                                ),
                              if (provider.parseValidationEntityModel?.issues
                                      ?.errors !=
                                  null)
                                for (var element in provider
                                    .parseValidationEntityModel!
                                    .issues!
                                    .errors!)
                                  Text(
                                    "${element["message"] ?? ''}\n",
                                    style: FontManager.getCustomStyle(
                                      fontSize: ResponsiveFontSizes.titleSmall(
                                          context),
                                      fontWeight: FontWeight.w400,
                                      fontFamily:
                                          FontManager.fontFamilyTiemposText,
                                      color: Colors.black,
                                    ),
                                  ),
                              if (provider.parseValidationEntityModel?.issues
                                      ?.warnings !=
                                  null)
                                for (var element in provider
                                    .parseValidationEntityModel!
                                    .issues!
                                    .warnings!)
                                  Text(
                                    "${element["message"] ?? ''}\n",
                                    style: FontManager.getCustomStyle(
                                      fontSize: ResponsiveFontSizes.titleSmall(
                                          context),
                                      fontWeight: FontWeight.w400,
                                      fontFamily:
                                          FontManager.fontFamilyTiemposText,
                                      color: Colors.black,
                                    ),
                                  ),
                              if (provider.parseValidationEntityModel?.issues
                                      ?.exceptions !=
                                  null)
                                for (var element in provider
                                    .parseValidationEntityModel!
                                    .issues!
                                    .exceptions!)
                                  Text(
                                    "${element["message"] ?? ''}\n",
                                    style: FontManager.getCustomStyle(
                                      fontSize: ResponsiveFontSizes.titleSmall(
                                          context),
                                      fontWeight: FontWeight.w400,
                                      fontFamily:
                                          FontManager.fontFamilyTiemposText,
                                      color: Colors.black,
                                    ),
                                  ),
                              if (provider.parseValidationEntityModel?.issues
                                      ?.validationErrors !=
                                  null)
                                for (var element in provider
                                    .parseValidationEntityModel!
                                    .issues!
                                    .validationErrors!)
                                  Text(
                                    "${element["message"] ?? ''}\n",
                                    style: FontManager.getCustomStyle(
                                      fontSize: ResponsiveFontSizes.titleSmall(
                                          context),
                                      fontWeight: FontWeight.w400,
                                      fontFamily:
                                          FontManager.fontFamilyTiemposText,
                                      color: Colors.black,
                                    ),
                                  ),
                              if (provider.parseValidationEntityModel?.issues
                                      ?.dependencyErrors !=
                                  null)
                                for (var element in provider
                                    .parseValidationEntityModel!
                                    .issues!
                                    .dependencyErrors!)
                                  Text(
                                    "${element["message"] ?? ''}\n",
                                    style: FontManager.getCustomStyle(
                                      fontSize: ResponsiveFontSizes.titleSmall(
                                          context),
                                      fontWeight: FontWeight.w400,
                                      fontFamily:
                                          FontManager.fontFamilyTiemposText,
                                      color: Colors.black,
                                    ),
                                  ),
                              if (provider.parseValidationEntityModel?.issues
                                      ?.uniquenessIssues !=
                                  null)
                                for (var element in provider
                                    .parseValidationEntityModel!
                                    .issues!
                                    .uniquenessIssues!)
                                  Text(
                                    "${element.message ?? ''}\n",
                                    style: FontManager.getCustomStyle(
                                      fontSize: ResponsiveFontSizes.titleSmall(
                                          context),
                                      fontWeight: FontWeight.w400,
                                      fontFamily:
                                          FontManager.fontFamilyTiemposText,
                                      color: Colors.black,
                                    ),
                                  ),
                              if (provider.parseValidationEntityModel?.issues
                                      ?.parsingIssues !=
                                  null)
                                for (var element in provider
                                    .parseValidationEntityModel!
                                    .issues!
                                    .parsingIssues!)
                                  Text(
                                    "${element["message"] ?? ''}\n",
                                    style: FontManager.getCustomStyle(
                                      fontSize: ResponsiveFontSizes.titleSmall(
                                          context),
                                      fontWeight: FontWeight.w400,
                                      fontFamily:
                                          FontManager.fontFamilyTiemposText,
                                      color: Colors.black,
                                    ),
                                  ),
                              if (provider.parseValidationEntityModel?.issues
                                      ?.mongoErrors !=
                                  null)
                                for (var element in provider
                                    .parseValidationEntityModel!
                                    .issues!
                                    .mongoErrors!)
                                  Text(
                                    "${element["message"] ?? ''}\n",
                                    style: FontManager.getCustomStyle(
                                      fontSize: ResponsiveFontSizes.titleSmall(
                                          context),
                                      fontWeight: FontWeight.w400,
                                      fontFamily:
                                          FontManager.fontFamilyTiemposText,
                                      color: Colors.black,
                                    ),
                                  ),
                              if (provider.parseValidationEntityModel?.issues
                                      ?.postgresErrors !=
                                  null)
                                for (var element in provider
                                    .parseValidationEntityModel!
                                    .issues!
                                    .postgresErrors!)
                                  Text(
                                    '${element["message"] ?? ''}\n',
                                    style: FontManager.getCustomStyle(
                                      fontSize: ResponsiveFontSizes.titleSmall(
                                          context),
                                      fontWeight: FontWeight.w400,
                                      fontFamily:
                                          FontManager.fontFamilyTiemposText,
                                      color: Colors.black,
                                    ),
                                  ),
                              if (provider.parseValidationEntityModel
                                          ?.validationResult?.structureErrors !=
                                      null &&
                                  provider
                                      .parseValidationEntityModel!
                                      .validationResult!
                                      .structureErrors!
                                      .isNotEmpty)
                                Text(
                                  provider.parseValidationEntityModel!
                                      .validationResult!.structureErrors!
                                      .join('\n'),
                                  style: FontManager.getCustomStyle(
                                    fontSize:
                                        ResponsiveFontSizes.titleSmall(context),
                                    fontWeight: FontWeight.w400,
                                    fontFamily:
                                        FontManager.fontFamilyTiemposText,
                                    color: Colors.black,
                                  ),
                                ),
                              if (provider
                                          .parseValidationEntityModel
                                          ?.validationResult
                                          ?.requiredFieldErrors !=
                                      null &&
                                  provider
                                      .parseValidationEntityModel!
                                      .validationResult!
                                      .requiredFieldErrors!
                                      .isNotEmpty)
                                Text(
                                  provider.parseValidationEntityModel!
                                      .validationResult!.requiredFieldErrors!
                                      .join('\n'),
                                  style: FontManager.getCustomStyle(
                                    fontSize:
                                        ResponsiveFontSizes.titleSmall(context),
                                    fontWeight: FontWeight.w400,
                                    fontFamily:
                                        FontManager.fontFamilyTiemposText,
                                    color: Colors.black,
                                  ),
                                ),
                              if (provider.parseValidationEntityModel
                                          ?.validationResult?.dataTypeErrors !=
                                      null &&
                                  provider
                                      .parseValidationEntityModel!
                                      .validationResult!
                                      .dataTypeErrors!
                                      .isNotEmpty)
                                Text(
                                  provider.parseValidationEntityModel!
                                      .validationResult!.dataTypeErrors!
                                      .join('\n'),
                                  style: FontManager.getCustomStyle(
                                    fontSize:
                                        ResponsiveFontSizes.titleSmall(context),
                                    fontWeight: FontWeight.w400,
                                    fontFamily:
                                        FontManager.fontFamilyTiemposText,
                                    color: Colors.black,
                                  ),
                                ),
                              if (provider.parseValidationEntityModel
                                          ?.validationResult?.customErrors !=
                                      null &&
                                  provider
                                      .parseValidationEntityModel!
                                      .validationResult!
                                      .customErrors!
                                      .isNotEmpty)
                                Text(
                                  provider.parseValidationEntityModel!
                                      .validationResult!.customErrors!
                                      .join('\n'),
                                  style: FontManager.getCustomStyle(
                                    fontSize:
                                        ResponsiveFontSizes.titleSmall(context),
                                    fontWeight: FontWeight.w400,
                                    fontFamily:
                                        FontManager.fontFamilyTiemposText,
                                    color: Colors.black,
                                  ),
                                ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ]),
            )),
            // Footer with top border
            Container(
              padding: const EdgeInsets.only(
                  bottom: 14, top: 14, left: 24, right: 24),
              decoration: const BoxDecoration(
                border: Border(
                  top: BorderSide(color: Color(0xFFE5E7EB), width: 1),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  ElevatedButton(
                    onPressed: () {
                      Navigator.of(context).pop(false);
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF0058FF),
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 24, vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      elevation: 0,
                    ),
                    child: Text(
                      'Proceed',
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.bodyMedium(context),
                        fontWeight: FontWeight.w500,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTableHeaders(List<String> tableHeaders,
      {ScrollController? scrollController}) {
    List scrollableHeaders = [];
    if (tableHeaders.length > 6) {
      scrollableHeaders = tableHeaders.sublist(0, tableHeaders.length - 1);
    }
    return tableHeaders.length > 6
        ? Container(
            decoration: BoxDecoration(
              color: Color(0xffF2F5F8),
              border: Border.all(color: Color(0xffB9C1CE)),
            ),
            child: Row(
              children: [
                // Scrollable part
                Expanded(
                  child: SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    controller: scrollController,
                    child: Row(
                      children: scrollableHeaders
                          .map(
                            (e) => Container(
                              width: 150,
                              alignment: Alignment.center,
                              padding: const EdgeInsets.symmetric(
                                  vertical: AppSpacing.xs),
                              child: Text(
                                e,
                                textAlign: TextAlign.center,
                                style: FontManager.getCustomStyle(
                                  color: Colors.black,
                                  fontFamily: FontManager.fontFamilyTiemposText,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                          )
                          .toList(),
                    ),
                  ),
                ),
                // Frozen column
                Container(
                  width: 100,
                  alignment: Alignment.center,
                  child: Text(
                    tableHeaders[tableHeaders.length - 1],
                    textAlign: TextAlign.center,
                    style: FontManager.getCustomStyle(
                      color: Colors.black,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                )
              ],
            ),
          )
        : Container(
            width: double.infinity,
            decoration: BoxDecoration(
              color: Color(0xffF2F5F8),
              border: Border.all(color: Color(0xffB9C1CE)),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.max,
              children: tableHeaders
                  .map(
                    (e) => Expanded(
                      child: Center(
                        child: Padding(
                          padding: const EdgeInsets.symmetric(
                              vertical: AppSpacing.xs),
                          child: Text(
                            e,
                            textAlign: TextAlign.center,
                            style: FontManager.getCustomStyle(
                                color: Colors.black,
                                fontFamily: FontManager.fontFamilyTiemposText,
                                fontWeight: FontWeight.w600),
                          ),
                        ),
                      ),
                    ),
                  )
                  .toList(),
            ));
  }

  Future<void> _showAttributeDialog({
    required BuildContext context,
    required String title,
    required CreateEntityProvider entityProvider,
  }) async {
    bool isEditMode = false;
    bool isFormValid = false;
    TextEditingController attributeNameController = TextEditingController();
    TextEditingController displayNameController = TextEditingController();
    String attDataType = entityProvider.attributesDataTypes[0];
    String attRequired = entityProvider.attributesRequired[0];
    String attUnique = entityProvider.attributesUnique[0];
    String selectedKeyType = 'Select One';
    List<ObjectAttribute> attributesList = [];
    int editIndex = -1;
    await showDialog(
      context: context,
      barrierDismissible:
          false, // Prevent dismissing to force unsaved changes check
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, childSetState) {
            if (attributeNameController.text.isNotEmpty &&
                displayNameController.text.isNotEmpty) {
              isFormValid = true;
            }
            attributeNameController.addListener(
              () {
                childSetState(() {});
              },
            );
            displayNameController.addListener(
              () {
                childSetState(() {});
              },
            );
            return PopScope(
              canPop: false,
              onPopInvokedWithResult: (bool didPop, dynamic result) async {
                if (didPop) return;

                // Check for unsaved changes only in add mode
                if (!isEditMode) {
                  bool hasUnsavedChanges = attributesList.isNotEmpty;

                  if (hasUnsavedChanges) {
                    bool shouldClose =
                        await _showUnsavedChangesWarning(context);
                    if (shouldClose) {
                      Navigator.of(context).pop();
                    }
                  } else {
                    Navigator.of(context).pop();
                  }
                } else {
                  Navigator.of(context).pop();
                }
              },
              child: Dialog(
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Container(
                  width: MediaQuery.of(context).size.width * 0.70,
                  height:
                      math.min(MediaQuery.of(context).size.height * 0.8, 600),
                  child: Column(
                    children: [
                      // Header with bottom border
                      Container(
                        padding: const EdgeInsets.all(24),
                        decoration: const BoxDecoration(
                          border: Border(
                            bottom:
                                BorderSide(color: Color(0xFFE5E7EB), width: 1),
                          ),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              title,
                              style: FontManager.getCustomStyle(
                                fontSize:
                                    ResponsiveFontSizes.bodyLarge(context),
                                fontWeight: FontWeight.w600,
                                fontFamily: FontManager.fontFamilyInter,
                                color: Colors.black,
                              ),
                            ),
                            Row(
                              children: [
                                if (!isEditMode)
                                  ElevatedButton(
                                    onPressed: isFormValid
                                        ? () {
                                            attributesList.add(ObjectAttribute(
                                              name:
                                                  attributeNameController.text,
                                              displayName:
                                                  displayNameController.text,
                                              dataType: attDataType,
                                              required: attRequired == "Yes"
                                                  ? true
                                                  : false,
                                              unique: attUnique == "Yes"
                                                  ? true
                                                  : false,
                                              isPrimaryKey: selectedKeyType ==
                                                  'Primary key',
                                              isForeignKey: selectedKeyType ==
                                                  'Foreign key',
                                            ));
                                            attributeNameController =
                                                TextEditingController();
                                            displayNameController =
                                                TextEditingController();
                                            attDataType = entityProvider
                                                .attributesDataTypes[0];
                                            attRequired = entityProvider
                                                .attributesRequired[0];
                                            attUnique = entityProvider
                                                .attributesUnique[0];
                                            selectedKeyType = 'Select One';
                                            childSetState(() {});
                                          }
                                        : null,
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: isFormValid
                                          ? const Color(0xFF0058FF)
                                          : Colors.grey[400],
                                      foregroundColor: Colors.white,
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 16, vertical: 8),
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(6),
                                      ),
                                      elevation: 0,
                                    ),
                                    child: Text(
                                      'Add More',
                                      style: FontManager.getCustomStyle(
                                        fontSize: ResponsiveFontSizes.bodySmall(
                                            context),
                                        fontWeight: FontWeight.w500,
                                        fontFamily:
                                            FontManager.fontFamilyTiemposText,
                                        color: Colors.white,
                                      ),
                                    ),
                                  ),
                                const SizedBox(width: 8),
                                IconButton(
                                  onPressed: () async {
                                    // Check for unsaved changes only in add mode
                                    if (!isEditMode) {
                                      bool hasUnsavedChanges =
                                          attributesList.isNotEmpty;

                                      if (hasUnsavedChanges) {
                                        bool shouldClose =
                                            await _showUnsavedChangesWarning(
                                                context);
                                        if (shouldClose) {
                                          Navigator.of(context).pop();
                                        }
                                      } else {
                                        Navigator.of(context).pop();
                                      }
                                    } else {
                                      Navigator.of(context).pop();
                                    }
                                  },
                                  icon: const Icon(Icons.close),
                                  iconSize: 24,
                                  color: Colors.grey[600],
                                  padding: EdgeInsets.zero,
                                  constraints: const BoxConstraints(),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),

                      // Scrollable content area
                      Expanded(
                        child: Row(
                          children: [
                            // Left side - Form (70% always)
                            Expanded(
                              flex: 6,
                              child: Padding(
                                  padding: const EdgeInsets.all(24),
                                  child: Column(
                                    spacing: AppSpacing.md,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Row(
                                        children: [
                                          Expanded(
                                              child: _buildEditableFormField(
                                                  context,
                                                  "Attribute Name",
                                                  attributeNameController)),
                                          const SizedBox(
                                            width: AppSpacing.sm,
                                          ),
                                          Expanded(
                                              child: _buildEditableFormField(
                                                  context,
                                                  "Display Name",
                                                  displayNameController))
                                        ],
                                      ),
                                      Row(
                                        children: [
                                          Expanded(
                                              child: _buildEditableDropdown(
                                                  context, "Data Type",
                                                  (value) {
                                            attDataType = value;
                                            childSetState(() {});
                                          }, entityProvider.attributesDataTypes,
                                                  attDataType)),
                                          const SizedBox(
                                            width: AppSpacing.sm,
                                          ),
                                          Expanded(
                                              child: _buildEditableDropdown(
                                                  context, "Required", (value) {
                                            attRequired = value;
                                            childSetState(() {});
                                          }, entityProvider.attributesRequired,
                                                  attRequired)),
                                        ],
                                      ),
                                      Row(
                                        children: [
                                          Expanded(
                                              child: _buildEditableDropdown(
                                                  context, "Unique", (value) {
                                            attUnique = value;
                                            childSetState(() {});
                                          }, entityProvider.attributesUnique,
                                                  attUnique)),
                                          const SizedBox(
                                            width: AppSpacing.sm,
                                          ),
                                          Expanded(child: Container())
                                        ],
                                      ),
                                      // Row(
                                      //   children: [
                                      //     Text(
                                      //       'Select One',
                                      //       style: FontManager.getCustomStyle(
                                      //         fontSize: ResponsiveFontSizes
                                      //             .bodyMedium(context),
                                      //         fontWeight: FontWeight.w500,
                                      //         fontFamily: FontManager
                                      //             .fontFamilyTiemposText,
                                      //         color: Colors.black,
                                      //       ),
                                      //     ),
                                      //     const SizedBox(width: 8),
                                      //     Row(
                                      //       children: [
                                      //         _buildKeyTypeButton(
                                      //             context,
                                      //             'Primary key',
                                      //             selectedKeyType ==
                                      //                 'Primary key', () {
                                      //           // Toggle behavior: if already selected, deselect it
                                      //           if (selectedKeyType ==
                                      //               'Primary key') {
                                      //             selectedKeyType =
                                      //                 'Select One';
                                      //           } else {
                                      //             selectedKeyType =
                                      //                 'Primary key';
                                      //           }
                                      //           childSetState(() {});
                                      //         }),
                                      //         const SizedBox(width: 8),
                                      //         _buildKeyTypeButton(
                                      //             context,
                                      //             'Foreign key',
                                      //             selectedKeyType ==
                                      //                 'Foreign key', () {
                                      //           if (selectedKeyType ==
                                      //               'Foreign key') {
                                      //             selectedKeyType =
                                      //                 'Select One';
                                      //           } else {
                                      //             selectedKeyType =
                                      //                 'Foreign key';
                                      //           }
                                      //           childSetState(() {});
                                      //         }),
                                      //       ],
                                      //     ),
                                      //   ],
                                      // )
                                    ],
                                  )),
                            ),
                            // Right side - Always show (30% space)
                            Expanded(
                              flex: 4,
                              child: Container(
                                decoration: const BoxDecoration(
                                  border: Border(
                                    left: BorderSide(
                                        color: Color(0xFFE5E7EB), width: 1),
                                  ),
                                  color: Colors.white,
                                ),
                                child: isEditMode
                                    ? Container()
                                    : Padding(
                                        padding: const EdgeInsets.all(24),
                                        child: ListView.builder(
                                          padding: EdgeInsets.zero,
                                          itemCount: attributesList.length,
                                          itemBuilder: (context, index) {
                                            final attribute =
                                                attributesList[index];
                                            return Padding(
                                              padding: const EdgeInsets.only(
                                                  bottom: 8),
                                              child: Row(
                                                children: [
                                                  Text(
                                                    '${index + 1}. ',
                                                    style: FontManager
                                                        .getCustomStyle(
                                                      fontSize:
                                                          ResponsiveFontSizes
                                                              .bodyMedium(
                                                                  context),
                                                      fontWeight:
                                                          FontWeight.w500,
                                                      fontFamily: FontManager
                                                          .fontFamilyTiemposText,
                                                      color: Colors.black,
                                                    ),
                                                  ),
                                                  Expanded(
                                                    child: Text(
                                                      attribute.name ??
                                                          'Attribute Name',
                                                      style: FontManager
                                                          .getCustomStyle(
                                                        fontSize:
                                                            ResponsiveFontSizes
                                                                .bodyMedium(
                                                                    context),
                                                        fontWeight:
                                                            FontWeight.w400,
                                                        fontFamily: FontManager
                                                            .fontFamilyTiemposText,
                                                        color: Colors.black,
                                                      ),
                                                    ),
                                                  ),
                                                  InkWell(
                                                    onTap: () async {
                                                      isEditMode = true;
                                                      editIndex = index;
                                                      attributeNameController =
                                                          TextEditingController(
                                                              text: attribute
                                                                  .name);
                                                      displayNameController =
                                                          TextEditingController(
                                                              text: attribute
                                                                  .displayName);
                                                      attDataType =
                                                          attribute.dataType ??
                                                              '';
                                                      attRequired =
                                                          (attribute.required ??
                                                                  false)
                                                              ? "Yes"
                                                              : "No";
                                                      attUnique =
                                                          (attribute.unique ??
                                                                  false)
                                                              ? "Yes"
                                                              : "No";
                                                      selectedKeyType = attribute
                                                              .isPrimaryKey
                                                          ? "Primary key"
                                                          : attribute
                                                                  .isForeignKey
                                                              ? "Foreign key"
                                                              : 'Select One';
                                                      childSetState(() {});
                                                    },
                                                    child: const Icon(
                                                        Icons.edit_outlined,
                                                        size: 16,
                                                        color: Colors.blue),
                                                  ),
                                                  const SizedBox(width: 8),
                                                  InkWell(
                                                    onTap: () {
                                                      attributesList
                                                          .remove(attribute);
                                                      childSetState(() {});
                                                    },
                                                    child: const Icon(
                                                        Icons.delete_outline,
                                                        size: 16,
                                                        color: Colors.red),
                                                  ),
                                                ],
                                              ),
                                            );
                                          },
                                        ),
                                      ), // Empty white space when list is not shown
                              ),
                            ),
                          ],
                        ),
                      ),

                      // Footer with top border
                      Container(
                        padding: const EdgeInsets.all(24),
                        decoration: const BoxDecoration(
                          border: Border(
                            top: BorderSide(color: Color(0xFFE5E7EB), width: 1),
                          ),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            TextButton(
                              onPressed: () async {
                                // Check for unsaved changes only in add mode
                                if (!isEditMode) {
                                  bool hasUnsavedChanges =
                                      attributesList.isNotEmpty;

                                  if (hasUnsavedChanges) {
                                    bool shouldClose =
                                        await _showUnsavedChangesWarning(
                                            context);
                                    if (shouldClose) {
                                      Navigator.of(context).pop();
                                    }
                                  } else {
                                    Navigator.of(context).pop();
                                  }
                                } else {
                                  isEditMode = false;
                                  editIndex = -1;
                                  attributeNameController =
                                      TextEditingController();
                                  displayNameController =
                                      TextEditingController();
                                  attDataType =
                                      entityProvider.attributesDataTypes[0];
                                  attRequired =
                                      entityProvider.attributesRequired[0];
                                  attUnique =
                                      entityProvider.attributesUnique[0];
                                  selectedKeyType = 'Select One';
                                  childSetState(() {});
                                }
                              },
                              style: TextButton.styleFrom(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 24, vertical: 12),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8),
                                  side: BorderSide(color: Colors.grey[300]!),
                                ),
                              ),
                              child: Text(
                                'Cancel',
                                style: FontManager.getCustomStyle(
                                  fontSize:
                                      ResponsiveFontSizes.bodyMedium(context),
                                  fontWeight: FontWeight.w500,
                                  fontFamily: FontManager.fontFamilyTiemposText,
                                  color: Colors.grey[700],
                                ),
                              ),
                            ),
                            const SizedBox(width: 16),
                            ElevatedButton(
                              onPressed: () async {
                                bool pop = true;
                                if (!isEditMode) {
                                  pop = await _validateEntity(context,
                                      entityProvider.currentEntity, title,
                                      attributeData: attributesList);
                                }
                                if (pop && !isEditMode) {
                                  entityProvider.currentEntity?.attributes
                                      ?.addAll(attributesList);

                                  Navigator.of(context).pop();
                                  entityProvider
                                      .updateEntityAttributeExpansion(false);
                                }
                                if (isEditMode && editIndex != -1) {
                                  final updatedAttribute = ObjectAttribute(
                                    name: attributeNameController.text,
                                    displayName: displayNameController.text,
                                    dataType: attDataType,
                                    required: attRequired == 'Yes',
                                    unique: attUnique == 'Yes',
                                    isPrimaryKey:
                                        selectedKeyType == 'Primary key',
                                    isForeignKey:
                                        selectedKeyType == 'Foreign key',
                                  );
                                  attributeNameController =
                                      TextEditingController();
                                  displayNameController =
                                      TextEditingController();
                                  attDataType =
                                      entityProvider.attributesDataTypes[0];
                                  attRequired =
                                      entityProvider.attributesRequired[0];
                                  attUnique =
                                      entityProvider.attributesUnique[0];
                                  selectedKeyType = 'Select One';
                                  isEditMode = false;

                                  childSetState(() {
                                    attributesList[editIndex] =
                                        updatedAttribute;
                                  });
                                }
                              },
                              style: ElevatedButton.styleFrom(
                                backgroundColor: const Color(0xFF0058FF),
                                foregroundColor: Colors.white,
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 24, vertical: 12),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                elevation: 0,
                              ),
                              child: Text(
                                isEditMode ? 'Update' : 'Validate',
                                style: FontManager.getCustomStyle(
                                  fontSize:
                                      ResponsiveFontSizes.bodyMedium(context),
                                  fontWeight: FontWeight.w500,
                                  fontFamily: FontManager.fontFamilyTiemposText,
                                  color: Colors.white,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }

  Future<void> _showRelationshipsDialog({
    required BuildContext context,
    required String title,
    required CreateEntityProvider entityProvider,
  }) async {
    bool isEditMode = false;
    bool isFormValid = false;
    TextEditingController descriptionController = TextEditingController();
    String? primaryKey;
    String? foreignKey;
    String? relationshipType = entityProvider.relationshipTypes[0];
    String? onDelete = entityProvider.onDeletes[0];
    String? onUpdate = entityProvider.onUpdates[0];
    String? foreignKeyType = entityProvider.foreignKeyTypes[0];

    List<EntityRelationship> relationshipList = [];
    int editIndex = -1;
    await showDialog(
      context: context,
      barrierDismissible:
          false, // Prevent dismissing to force unsaved changes check
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, childSetState) {
            if (primaryKey != null && foreignKey != null) {
              isFormValid = true;
            }

            descriptionController.addListener(
              () {
                childSetState(() {});
              },
            );
            return PopScope(
              canPop: false,
              onPopInvokedWithResult: (bool didPop, dynamic result) async {
                if (didPop) return;

                // Check for unsaved changes only in add mode
                if (!isEditMode) {
                  bool hasUnsavedChanges = relationshipList.isNotEmpty;

                  if (hasUnsavedChanges) {
                    bool shouldClose =
                        await _showUnsavedChangesWarning(context);
                    if (shouldClose) {
                      Navigator.of(context).pop();
                    }
                  } else {
                    Navigator.of(context).pop();
                  }
                } else {
                  Navigator.of(context).pop();
                }
              },
              child: Dialog(
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Container(
                  width: MediaQuery.of(context).size.width * 0.75,
                  height:
                      math.min(MediaQuery.of(context).size.height * 0.8, 600),
                  child: Column(
                    children: [
                      // Header with bottom border
                      Container(
                        padding: const EdgeInsets.all(24),
                        decoration: const BoxDecoration(
                          border: Border(
                            bottom:
                                BorderSide(color: Color(0xFFE5E7EB), width: 1),
                          ),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              title,
                              style: FontManager.getCustomStyle(
                                fontSize:
                                    ResponsiveFontSizes.bodyLarge(context),
                                fontWeight: FontWeight.w600,
                                fontFamily: FontManager.fontFamilyInter,
                                color: Colors.black,
                              ),
                            ),
                            Row(
                              children: [
                                if (!isEditMode)
                                  ElevatedButton(
                                    onPressed: isFormValid
                                        ? () {
                                            relationshipList.add(
                                                EntityRelationship(
                                                    primaryKey: primaryKey,
                                                    foreignKey: foreignKey,
                                                    relationshipType:
                                                        relationshipType,
                                                    onDelete: onDelete,
                                                    onUpdate: onUpdate,
                                                    foreignKeyType:
                                                        foreignKeyType,
                                                    description:
                                                        descriptionController
                                                            .text));
                                            descriptionController =
                                                TextEditingController();
                                            primaryKey = null;
                                            foreignKey = null;
                                            relationshipType = entityProvider
                                                .relationshipTypes[0];
                                            onDelete =
                                                entityProvider.onDeletes[0];
                                            onUpdate =
                                                entityProvider.onUpdates[0];
                                            foreignKeyType = entityProvider
                                                .foreignKeyTypes[0];
                                            childSetState(() {});
                                          }
                                        : null,
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: isFormValid
                                          ? const Color(0xFF0058FF)
                                          : Colors.grey[400],
                                      foregroundColor: Colors.white,
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 16, vertical: 8),
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(6),
                                      ),
                                      elevation: 0,
                                    ),
                                    child: Text(
                                      'Add More',
                                      style: FontManager.getCustomStyle(
                                        fontSize: ResponsiveFontSizes.bodySmall(
                                            context),
                                        fontWeight: FontWeight.w500,
                                        fontFamily:
                                            FontManager.fontFamilyTiemposText,
                                        color: Colors.white,
                                      ),
                                    ),
                                  ),
                                const SizedBox(width: 8),
                                IconButton(
                                  onPressed: () async {
                                    // Check for unsaved changes only in add mode
                                    if (!isEditMode) {
                                      bool hasUnsavedChanges =
                                          relationshipList.isNotEmpty;

                                      if (hasUnsavedChanges) {
                                        bool shouldClose =
                                            await _showUnsavedChangesWarning(
                                                context);
                                        if (shouldClose) {
                                          Navigator.of(context).pop();
                                        }
                                      } else {
                                        Navigator.of(context).pop();
                                      }
                                    } else {
                                      Navigator.of(context).pop();
                                    }
                                  },
                                  icon: const Icon(Icons.close),
                                  iconSize: 24,
                                  color: Colors.grey[600],
                                  padding: EdgeInsets.zero,
                                  constraints: const BoxConstraints(),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),

                      // Scrollable content area
                      Expanded(
                        child: Row(
                          children: [
                            // Left side - Form (70% always)
                            Expanded(
                              flex: 7,
                              child: Padding(
                                  padding: const EdgeInsets.all(24),
                                  child: Column(
                                    spacing: AppSpacing.md,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Row(
                                        children: [
                                          Expanded(
                                              child: Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              _buildEditableDropdown(
                                                  context, "Primary Key",
                                                  (value) {
                                                primaryKey = value;
                                                childSetState(() {});
                                              },
                                                  entityProvider.currentEntity
                                                          ?.attributes
                                                          ?.map(
                                                            (e) => e.name,
                                                          )
                                                          .toList() ??
                                                      [],
                                                  primaryKey),
                                              const SizedBox(
                                                height: 2,
                                              ),
                                              if ((entityProvider.currentEntity
                                                          ?.attributes ??
                                                      [])
                                                  .isEmpty)
                                                Text(
                                                  "Attributes are empty, please create attributes",
                                                  style: FontManager.getCustomStyle(
                                                      fontFamily: FontManager
                                                          .fontFamilyTiemposText,
                                                      color: Colors.red,
                                                      fontSize: FontManager.s8),
                                                )
                                            ],
                                          )),
                                          SizedBox(
                                            width: AppSpacing.xl,
                                            child: Center(
                                              child: Text(
                                                "OR",
                                                style: FontManager.getCustomStyle(
                                                    fontFamily: FontManager
                                                        .fontFamilyTiemposText),
                                              ),
                                            ),
                                          ),
                                          Expanded(
                                              child: Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              _buildEditableDropdown(
                                                  context, "Foreign Key",
                                                  (value) {
                                                foreignKey = value;
                                                childSetState(() {});
                                              },
                                                  entityProvider.currentEntity
                                                          ?.attributes
                                                          ?.map(
                                                            (e) => e.name,
                                                          )
                                                          .toList() ??
                                                      [],
                                                  foreignKey),
                                              const SizedBox(
                                                height: 2,
                                              ),
                                              if ((entityProvider.currentEntity
                                                          ?.attributes ??
                                                      [])
                                                  .isEmpty)
                                                Text(
                                                  "Attributes are empty, please create attributes",
                                                  style: FontManager.getCustomStyle(
                                                      fontFamily: FontManager
                                                          .fontFamilyTiemposText,
                                                      color: Colors.red,
                                                      fontSize: FontManager.s8),
                                                )
                                            ],
                                          )),
                                          const SizedBox(
                                            width: AppSpacing.xl,
                                          ),
                                          Expanded(
                                            child: Container(),
                                          )
                                        ],
                                      ),
                                      Row(
                                        children: [
                                          Expanded(
                                              child: _buildEditableDropdown(
                                                  context, "Relationship Type",
                                                  (value) {
                                            relationshipType = value;
                                            childSetState(() {});
                                          }, entityProvider.relationshipTypes,
                                                  relationshipType)),
                                          const SizedBox(
                                            width: AppSpacing.xl,
                                          ),
                                          Expanded(
                                              child: _buildEditableDropdown(
                                                  context, "On Delete",
                                                  (value) {
                                            onDelete = value;
                                            childSetState(() {});
                                          }, entityProvider.onDeletes,
                                                  onDelete)),
                                          const SizedBox(
                                            width: AppSpacing.xl,
                                          ),
                                          Expanded(
                                              child: _buildEditableDropdown(
                                                  context, "On Update",
                                                  (value) {
                                            onUpdate = value;
                                            childSetState(() {});
                                          }, entityProvider.onUpdates,
                                                  onUpdate)),
                                        ],
                                      ),
                                      Row(
                                        children: [
                                          Expanded(
                                              child: _buildEditableDropdown(
                                                  context, "Foreign Key Type",
                                                  (value) {
                                            foreignKeyType = value;
                                            childSetState(() {});
                                          }, entityProvider.foreignKeyTypes,
                                                  foreignKeyType)),
                                          const SizedBox(
                                            width: AppSpacing.xl,
                                          ),
                                          Expanded(
                                              flex: 2,
                                              child: _buildEditableFormField(
                                                  context,
                                                  "Description",
                                                  descriptionController,
                                                  requireLabel: false))
                                        ],
                                      ),
                                    ],
                                  )),
                            ),
                            // Right side - Always show (30% space)
                            Expanded(
                              flex: 3,
                              child: Container(
                                decoration: const BoxDecoration(
                                  border: Border(
                                    left: BorderSide(
                                        color: Color(0xFFE5E7EB), width: 1),
                                  ),
                                  color: Colors.white,
                                ),
                                child: isEditMode
                                    ? Container()
                                    : Padding(
                                        padding: const EdgeInsets.all(24),
                                        child: ListView.builder(
                                          padding: EdgeInsets.zero,
                                          itemCount: relationshipList.length,
                                          itemBuilder: (context, index) {
                                            final relationship =
                                                relationshipList[index];
                                            return Padding(
                                              padding: const EdgeInsets.only(
                                                  bottom: 8),
                                              child: Row(
                                                children: [
                                                  Text(
                                                    '${index + 1}. ',
                                                    style: FontManager
                                                        .getCustomStyle(
                                                      fontSize:
                                                          ResponsiveFontSizes
                                                              .bodyMedium(
                                                                  context),
                                                      fontWeight:
                                                          FontWeight.w500,
                                                      fontFamily: FontManager
                                                          .fontFamilyTiemposText,
                                                      color: Colors.black,
                                                    ),
                                                  ),
                                                  Expanded(
                                                    child: Text(
                                                      relationship.primaryKey ??
                                                          '',
                                                      style: FontManager
                                                          .getCustomStyle(
                                                        fontSize:
                                                            ResponsiveFontSizes
                                                                .bodyMedium(
                                                                    context),
                                                        fontWeight:
                                                            FontWeight.w400,
                                                        fontFamily: FontManager
                                                            .fontFamilyTiemposText,
                                                        color: Colors.black,
                                                      ),
                                                    ),
                                                  ),
                                                  InkWell(
                                                    onTap: () async {
                                                      isEditMode = true;
                                                      editIndex = index;
                                                      descriptionController
                                                          .text = relationship
                                                              .description ??
                                                          '';
                                                      primaryKey = relationship
                                                          .primaryKey;
                                                      foreignKey = relationship
                                                          .foreignKey;
                                                      relationshipType =
                                                          relationship
                                                              .relationshipType;
                                                      onDelete =
                                                          relationship.onDelete;
                                                      onUpdate =
                                                          relationship.onUpdate;
                                                      foreignKeyType =
                                                          relationship
                                                              .foreignKeyType;

                                                      childSetState(() {});
                                                    },
                                                    child: const Icon(
                                                        Icons.edit_outlined,
                                                        size: 16,
                                                        color: Colors.blue),
                                                  ),
                                                  const SizedBox(width: 8),
                                                  InkWell(
                                                    onTap: () {
                                                      relationshipList
                                                          .remove(relationship);
                                                      childSetState(() {});
                                                    },
                                                    child: const Icon(
                                                        Icons.delete_outline,
                                                        size: 16,
                                                        color: Colors.red),
                                                  ),
                                                ],
                                              ),
                                            );
                                          },
                                        ),
                                      ), // Empty white space when list is not shown
                              ),
                            ),
                          ],
                        ),
                      ),

                      // Footer with top border
                      Container(
                        padding: const EdgeInsets.all(24),
                        decoration: const BoxDecoration(
                          border: Border(
                            top: BorderSide(color: Color(0xFFE5E7EB), width: 1),
                          ),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            TextButton(
                              onPressed: () async {
                                // Check for unsaved changes only in add mode
                                if (!isEditMode) {
                                  bool hasUnsavedChanges =
                                      relationshipList.isNotEmpty;

                                  if (hasUnsavedChanges) {
                                    bool shouldClose =
                                        await _showUnsavedChangesWarning(
                                            context);
                                    if (shouldClose) {
                                      Navigator.of(context).pop();
                                    }
                                  } else {
                                    Navigator.of(context).pop();
                                  }
                                } else {
                                  isEditMode = false;
                                  editIndex = -1;
                                  descriptionController =
                                      TextEditingController();
                                  primaryKey = null;
                                  foreignKey = null;
                                  relationshipType =
                                      entityProvider.relationshipTypes[0];
                                  onDelete = entityProvider.onDeletes[0];
                                  onUpdate = entityProvider.onUpdates[0];
                                  foreignKeyType =
                                      entityProvider.foreignKeyTypes[0];
                                  childSetState(() {});
                                }
                              },
                              style: TextButton.styleFrom(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 24, vertical: 12),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8),
                                  side: BorderSide(color: Colors.grey[300]!),
                                ),
                              ),
                              child: Text(
                                'Cancel',
                                style: FontManager.getCustomStyle(
                                  fontSize:
                                      ResponsiveFontSizes.bodyMedium(context),
                                  fontWeight: FontWeight.w500,
                                  fontFamily: FontManager.fontFamilyTiemposText,
                                  color: Colors.grey[700],
                                ),
                              ),
                            ),
                            const SizedBox(width: 16),
                            ElevatedButton(
                              onPressed: () async {
                                bool pop = true;
                                if (!isEditMode) {
                                  pop = await _validateEntity(context,
                                      entityProvider.currentEntity, title,
                                      relationshipData: relationshipList);
                                }
                                if (pop && !isEditMode) {
                                  entityProvider.currentEntity?.relationships
                                      ?.addAll(relationshipList);
                                  entityProvider.setupScrollSync();

                                  Navigator.of(context).pop();
                                  entityProvider
                                      .updateEntityRelationShipExpansion(false);
                                }
                                if (isEditMode && editIndex != -1) {
                                  final updatedRelationship =
                                      EntityRelationship(
                                          primaryKey: primaryKey,
                                          foreignKey: foreignKey,
                                          relationshipType: relationshipType,
                                          onDelete: onDelete,
                                          onUpdate: onUpdate,
                                          foreignKeyType: foreignKeyType,
                                          description:
                                              descriptionController.text);
                                  descriptionController =
                                      TextEditingController();
                                  primaryKey = null;
                                  foreignKey = null;
                                  relationshipType =
                                      entityProvider.relationshipTypes[0];
                                  onDelete = entityProvider.onDeletes[0];
                                  onUpdate = entityProvider.onUpdates[0];
                                  foreignKeyType =
                                      entityProvider.foreignKeyTypes[0];
                                  isEditMode = false;

                                  childSetState(() {
                                    relationshipList[editIndex] =
                                        updatedRelationship;
                                  });
                                }
                              },
                              style: ElevatedButton.styleFrom(
                                backgroundColor: const Color(0xFF0058FF),
                                foregroundColor: Colors.white,
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 24, vertical: 12),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                elevation: 0,
                              ),
                              child: Text(
                                isEditMode ? 'Update' : 'Validate',
                                style: FontManager.getCustomStyle(
                                  fontSize:
                                      ResponsiveFontSizes.bodyMedium(context),
                                  fontWeight: FontWeight.w500,
                                  fontFamily: FontManager.fontFamilyTiemposText,
                                  color: Colors.white,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }

  Future<void> _showEnumDialog({
    required BuildContext context,
    required String title,
    required CreateEntityProvider entityProvider,
  }) async {
    bool isEditMode = false;
    bool isFormValid = false;

    String? attribute;
    List<EnumSpecial> enums = [
      EnumSpecial(1, TextEditingController()),
    ];
    List<EnumValue> enumValueList = [];
    int editIndex = -1;
    await showDialog(
      context: context,
      barrierDismissible:
          false, // Prevent dismissing to force unsaved changes check
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, childSetState) {
            if (attribute != null && enums.isNotEmpty) {
              isFormValid = true;
            }

            return PopScope(
              canPop: false,
              onPopInvokedWithResult: (bool didPop, dynamic result) async {
                if (didPop) return;

                // Check for unsaved changes only in add mode
                if (!isEditMode) {
                  bool hasUnsavedChanges = enumValueList.isNotEmpty;

                  if (hasUnsavedChanges) {
                    bool shouldClose =
                        await _showUnsavedChangesWarning(context);
                    if (shouldClose) {
                      Navigator.of(context).pop();
                    }
                  } else {
                    Navigator.of(context).pop();
                  }
                } else {
                  Navigator.of(context).pop();
                }
              },
              child: Dialog(
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Container(
                  width: MediaQuery.of(context).size.width * 0.75,
                  height:
                      math.min(MediaQuery.of(context).size.height * 0.8, 600),
                  child: Column(
                    children: [
                      // Header with bottom border
                      Container(
                        padding: const EdgeInsets.all(24),
                        decoration: const BoxDecoration(
                          border: Border(
                            bottom:
                                BorderSide(color: Color(0xFFE5E7EB), width: 1),
                          ),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              title,
                              style: FontManager.getCustomStyle(
                                fontSize:
                                    ResponsiveFontSizes.bodyLarge(context),
                                fontWeight: FontWeight.w600,
                                fontFamily: FontManager.fontFamilyInter,
                                color: Colors.black,
                              ),
                            ),
                            Row(
                              children: [
                                if (!isEditMode)
                                  ElevatedButton(
                                    onPressed: isFormValid
                                        ? () {
                                            enumValueList.add(EnumValue(
                                                entityAttribute: attribute,
                                                value: enums
                                                    .map(
                                                      (e) =>
                                                          e.textController.text,
                                                    )
                                                    .toList()
                                                    .join(",")));

                                            attribute = null;
                                            enums = [
                                              EnumSpecial(
                                                  1, TextEditingController()),
                                            ];
                                            childSetState(() {});
                                          }
                                        : null,
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: isFormValid
                                          ? const Color(0xFF0058FF)
                                          : Colors.grey[400],
                                      foregroundColor: Colors.white,
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 16, vertical: 8),
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(6),
                                      ),
                                      elevation: 0,
                                    ),
                                    child: Text(
                                      'Add More',
                                      style: FontManager.getCustomStyle(
                                        fontSize: ResponsiveFontSizes.bodySmall(
                                            context),
                                        fontWeight: FontWeight.w500,
                                        fontFamily:
                                            FontManager.fontFamilyTiemposText,
                                        color: Colors.white,
                                      ),
                                    ),
                                  ),
                                const SizedBox(width: 8),
                                IconButton(
                                  onPressed: () async {
                                    // Check for unsaved changes only in add mode
                                    if (!isEditMode) {
                                      bool hasUnsavedChanges =
                                          enumValueList.isNotEmpty;

                                      if (hasUnsavedChanges) {
                                        bool shouldClose =
                                            await _showUnsavedChangesWarning(
                                                context);
                                        if (shouldClose) {
                                          Navigator.of(context).pop();
                                        }
                                      } else {
                                        Navigator.of(context).pop();
                                      }
                                    } else {
                                      Navigator.of(context).pop();
                                    }
                                  },
                                  icon: const Icon(Icons.close),
                                  iconSize: 24,
                                  color: Colors.grey[600],
                                  padding: EdgeInsets.zero,
                                  constraints: const BoxConstraints(),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),

                      // Scrollable content area
                      Expanded(
                        child: Row(
                          children: [
                            // Left side - Form (70% always)
                            Expanded(
                              flex: 7,
                              child: Padding(
                                  padding: const EdgeInsets.all(24),
                                  child: Align(
                                    alignment: Alignment.topCenter,
                                    child: SingleChildScrollView(
                                      child: Column(
                                        mainAxisAlignment:
                                            MainAxisAlignment.start,
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Row(
                                            children: [
                                              Expanded(
                                                  child: Row(
                                                children: [
                                                  Container(
                                                    decoration: BoxDecoration(
                                                      shape: BoxShape.circle,
                                                      border: Border.all(
                                                          color: Colors.white),
                                                      color: Colors.white,
                                                    ),
                                                    padding: EdgeInsets.all(
                                                        AppSpacing.xs),
                                                    margin:
                                                        EdgeInsets.symmetric(
                                                            horizontal:
                                                                AppSpacing.xs,
                                                            vertical:
                                                                AppSpacing.xs),
                                                    child: Text(
                                                      "1",
                                                      style: FontManager
                                                          .getCustomStyle(
                                                              fontFamily:
                                                                  FontManager
                                                                      .fontFamilyTiemposText,
                                                              color:
                                                                  Colors.white),
                                                    ),
                                                  ),
                                                  const SizedBox(
                                                    width: AppSpacing.xs,
                                                  ),
                                                  Expanded(
                                                    child: Column(
                                                      crossAxisAlignment:
                                                          CrossAxisAlignment
                                                              .start,
                                                      children: [
                                                        _buildEditableDropdown(
                                                            context,
                                                            "Attribute",
                                                            (value) {
                                                          attribute = value;
                                                          childSetState(() {});
                                                        },
                                                            entityProvider
                                                                    .currentEntity
                                                                    ?.attributes
                                                                    ?.map(
                                                                      (e) => e
                                                                          .name,
                                                                    )
                                                                    .toList() ??
                                                                [],
                                                            attribute),
                                                        const SizedBox(
                                                          height: 2,
                                                        ),
                                                        if ((entityProvider
                                                                    .currentEntity
                                                                    ?.attributes ??
                                                                [])
                                                            .isEmpty)
                                                          Text(
                                                            "Attributes are empty, please create attributes",
                                                            style: FontManager
                                                                .getCustomStyle(
                                                                    fontFamily:
                                                                        FontManager
                                                                            .fontFamilyTiemposText,
                                                                    color: Colors
                                                                        .red,
                                                                    fontSize:
                                                                        FontManager
                                                                            .s8),
                                                          )
                                                      ],
                                                    ),
                                                  ),
                                                ],
                                              )),
                                              SizedBox(
                                                width: AppSpacing.sm,
                                              ),
                                              Expanded(
                                                child: Container(),
                                              )
                                            ],
                                          ),
                                          SizedBox(
                                            height: AppSpacing.md,
                                          ),
                                          Row(
                                            children: [
                                              Expanded(
                                                  child: Row(
                                                children: [
                                                  Container(
                                                    decoration: BoxDecoration(
                                                      shape: BoxShape.circle,
                                                      border: Border.all(
                                                          color: Colors.white),
                                                      color: Colors.white,
                                                    ),
                                                    padding: EdgeInsets.all(
                                                        AppSpacing.xs),
                                                    margin:
                                                        EdgeInsets.symmetric(
                                                      horizontal: AppSpacing.xs,
                                                      // vertical:
                                                      //     AppSpacing.xs
                                                    ),
                                                    child: Text(
                                                      "1",
                                                      style: FontManager
                                                          .getCustomStyle(
                                                              fontFamily:
                                                                  FontManager
                                                                      .fontFamilyTiemposText,
                                                              color:
                                                                  Colors.white),
                                                    ),
                                                  ),
                                                  const SizedBox(
                                                    width: AppSpacing.xs,
                                                  ),
                                                  Expanded(
                                                      child: Text(
                                                    "Enum Name",
                                                    style: FontManager.getCustomStyle(
                                                        fontFamily: FontManager
                                                            .fontFamilyTiemposText),
                                                  )),
                                                ],
                                              )),
                                              SizedBox(
                                                width: AppSpacing.sm,
                                              ),
                                              Expanded(
                                                child: Container(),
                                              )
                                            ],
                                          ),
                                          ReorderableListView.builder(
                                              shrinkWrap: true,
                                              buildDefaultDragHandles: false,
                                              padding: EdgeInsets.zero,
                                              itemBuilder: (context, index) {
                                                final e = enums[index];
                                                return Row(
                                                  key: ValueKey(e.hashCode),
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.end,
                                                  children: [
                                                    Container(
                                                      decoration: BoxDecoration(
                                                        shape: BoxShape.circle,
                                                        border: Border.all(
                                                            color:
                                                                Colors.black),
                                                        color: Colors.white,
                                                      ),
                                                      padding: EdgeInsets.all(
                                                          AppSpacing.xs),
                                                      margin:
                                                          EdgeInsets.symmetric(
                                                              horizontal:
                                                                  AppSpacing.xs,
                                                              vertical:
                                                                  AppSpacing
                                                                      .xxs),
                                                      child: Text(
                                                        // e.index.toString(),
                                                        (index + 1).toString(),
                                                        style: FontManager
                                                            .getCustomStyle(
                                                          fontFamily: FontManager
                                                              .fontFamilyTiemposText,
                                                        ),
                                                      ),
                                                    ),
                                                    const SizedBox(
                                                      width: AppSpacing.xs,
                                                    ),
                                                    Expanded(
                                                      child: Padding(
                                                        padding: EdgeInsets
                                                            .symmetric(
                                                                vertical:
                                                                    AppSpacing
                                                                        .xxs),
                                                        child:
                                                            _buildEditableFormField(
                                                                context,
                                                                "",
                                                                e.textController),
                                                      ),
                                                    ),
                                                    const SizedBox(
                                                      width: AppSpacing.xs,
                                                    ),
                                                    // Padding(
                                                    //   padding:
                                                    //       EdgeInsets.symmetric(
                                                    //           vertical:
                                                    //               AppSpacing
                                                    //                   .xs),
                                                    //   child:
                                                    //       _buildKeyTypeButton(
                                                    //           context,
                                                    //           'User can edit',
                                                    //           e.canEdit == true,
                                                    //           () {
                                                    //     if (e.canEdit == true) {
                                                    //       e.canEdit = null;
                                                    //     } else {
                                                    //       e.canEdit = true;
                                                    //     }
                                                    //     childSetState(() {});
                                                    //   }),
                                                    // ),
                                                    // const SizedBox(
                                                    //   width: AppSpacing.xs,
                                                    // ),
                                                    // Padding(
                                                    //   padding:
                                                    //       EdgeInsets.symmetric(
                                                    //           vertical:
                                                    //               AppSpacing
                                                    //                   .xs),
                                                    //   child: _buildKeyTypeButton(
                                                    //       context,
                                                    //       'User cannot edit',
                                                    //       e.canEdit == false,
                                                    //       () {
                                                    //     if (e.canEdit ==
                                                    //         false) {
                                                    //       e.canEdit = null;
                                                    //     } else {
                                                    //       e.canEdit = false;
                                                    //     }
                                                    //     childSetState(() {});
                                                    //   }),
                                                    // ),
                                                    const SizedBox(
                                                        width: AppSpacing.xs),
                                                    Padding(
                                                      padding:
                                                          EdgeInsets.symmetric(
                                                              vertical:
                                                                  AppSpacing
                                                                          .xxs +
                                                                      6),
                                                      child: InkWell(
                                                        onTap: () {
                                                          enums.remove(e);
                                                          childSetState(() {});
                                                        },
                                                        child: const Icon(
                                                            Icons
                                                                .delete_outline,
                                                            size: 16,
                                                            color: Colors.red),
                                                      ),
                                                    ),
                                                    const SizedBox(
                                                        width: AppSpacing.xs),
                                                    Column(
                                                      mainAxisSize:
                                                          MainAxisSize.max,
                                                      mainAxisAlignment:
                                                          MainAxisAlignment
                                                              .center,
                                                      children: [
                                                        ReorderableDragStartListener(
                                                          index: index,
                                                          child: Container(
                                                            padding:
                                                                EdgeInsets.all(
                                                                    AppSpacing
                                                                        .xxs),
                                                            child: Icon(
                                                                Icons
                                                                    .drag_handle,
                                                                color: Colors
                                                                    .grey),
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                  ],
                                                );
                                              },
                                              itemCount: enums.length,
                                              onReorder: (oldIndex, newIndex) {
                                                if (newIndex > oldIndex) {
                                                  newIndex -= 1;
                                                }

                                                final item =
                                                    enums.removeAt(oldIndex);
                                                enums.insert(newIndex, item);
                                                childSetState(() {});
                                              }),
                                          SizedBox(
                                            height: AppSpacing.xxs / 2,
                                          ),
                                          Row(
                                            children: [
                                              IconButton(
                                                  padding: EdgeInsets.zero,
                                                  onPressed: () {
                                                    enums.add(EnumSpecial(
                                                        enums.length + 1,
                                                        TextEditingController()));
                                                    childSetState(() {});
                                                  },
                                                  icon: Icon(
                                                    Icons.add,
                                                    size: 20,
                                                    color: Colors.black,
                                                  )),
                                              SizedBox(
                                                width: AppSpacing.sm,
                                              ),
                                              ElevatedButton(
                                                onPressed: () async {
                                                  List fileValues =
                                                      await entityProvider
                                                          .pickAndReadFileLines();
                                                  for (var element
                                                      in fileValues) {
                                                    enums.add(EnumSpecial(
                                                        enums.length + 1,
                                                        TextEditingController(
                                                            text: element)));
                                                  }
                                                  childSetState(() {});
                                                },
                                                style: ElevatedButton.styleFrom(
                                                  backgroundColor: Colors.black,
                                                  foregroundColor: Colors.white,
                                                  padding: const EdgeInsets
                                                      .symmetric(
                                                      horizontal: 24,
                                                      vertical: 12),
                                                  shape: RoundedRectangleBorder(
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            8),
                                                  ),
                                                  elevation: 0,
                                                ),
                                                child: Text(
                                                  'Browse',
                                                  style: FontManager
                                                      .getCustomStyle(
                                                    fontSize:
                                                        ResponsiveFontSizes
                                                            .bodyMedium(
                                                                context),
                                                    fontWeight: FontWeight.w500,
                                                    fontFamily: FontManager
                                                        .fontFamilyTiemposText,
                                                    color: Colors.white,
                                                  ),
                                                ),
                                              ),
                                            ],
                                          )
                                        ],
                                      ),
                                    ),
                                  )),
                            ),
                            // Right side - Always show (30% space)
                            Expanded(
                              flex: 3,
                              child: Container(
                                decoration: const BoxDecoration(
                                  border: Border(
                                    left: BorderSide(
                                        color: Color(0xFFE5E7EB), width: 1),
                                  ),
                                  color: Colors.white,
                                ),
                                child: isEditMode
                                    ? Container()
                                    : Padding(
                                        padding: const EdgeInsets.all(24),
                                        child: enumValueList.isEmpty
                                            ? Container()
                                            : ListView.builder(
                                                padding: EdgeInsets.zero,
                                                itemCount: enumValueList.length,
                                                itemBuilder: (context, index) {
                                                  final enumValue =
                                                      enumValueList[index];
                                                  return Padding(
                                                    padding:
                                                        const EdgeInsets.only(
                                                            bottom: 8),
                                                    child: Row(
                                                      children: [
                                                        Text(
                                                          '${index + 1}. ',
                                                          style: FontManager
                                                              .getCustomStyle(
                                                            fontSize:
                                                                ResponsiveFontSizes
                                                                    .bodyMedium(
                                                                        context),
                                                            fontWeight:
                                                                FontWeight.w500,
                                                            fontFamily: FontManager
                                                                .fontFamilyTiemposText,
                                                            color: Colors.black,
                                                          ),
                                                        ),
                                                        Expanded(
                                                          child: Text(
                                                            "${enumValue.entityAttribute} -> ${enumValue.value}",
                                                            maxLines: 1,
                                                            overflow:
                                                                TextOverflow
                                                                    .ellipsis,
                                                            style: FontManager
                                                                .getCustomStyle(
                                                              fontSize:
                                                                  ResponsiveFontSizes
                                                                      .bodyMedium(
                                                                          context),
                                                              fontWeight:
                                                                  FontWeight
                                                                      .w400,
                                                              fontFamily:
                                                                  FontManager
                                                                      .fontFamilyTiemposText,
                                                              color:
                                                                  Colors.black,
                                                            ),
                                                          ),
                                                        ),
                                                        InkWell(
                                                          onTap: () async {
                                                            isEditMode = true;
                                                            editIndex = index;
                                                            attribute = enumValue
                                                                .entityAttribute;

                                                            List temp = enumValue
                                                                    .value
                                                                    ?.split(
                                                                        ',') ??
                                                                [];
                                                            enums = temp
                                                                    .map(
                                                                      (e) => EnumSpecial(
                                                                          temp.indexOf(e) +
                                                                              1,
                                                                          TextEditingController(
                                                                              text:
                                                                                  e),
                                                                          value:
                                                                              e),
                                                                    )
                                                                    .toList() ??
                                                                [];

                                                            childSetState(
                                                                () {});
                                                          },
                                                          child: const Icon(
                                                              Icons
                                                                  .edit_outlined,
                                                              size: 16,
                                                              color:
                                                                  Colors.blue),
                                                        ),
                                                        const SizedBox(
                                                            width: 8),
                                                        InkWell(
                                                          onTap: () {
                                                            enumValueList
                                                                .remove(
                                                                    enumValue);
                                                            childSetState(
                                                                () {});
                                                          },
                                                          child: const Icon(
                                                              Icons
                                                                  .delete_outline,
                                                              size: 16,
                                                              color:
                                                                  Colors.red),
                                                        ),
                                                      ],
                                                    ),
                                                  );
                                                },
                                              ),
                                      ), // Empty white space when list is not shown
                              ),
                            ),
                          ],
                        ),
                      ),

                      // Footer with top border
                      Container(
                        padding: const EdgeInsets.all(24),
                        decoration: const BoxDecoration(
                          border: Border(
                            top: BorderSide(color: Color(0xFFE5E7EB), width: 1),
                          ),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            TextButton(
                              onPressed: () async {
                                // Check for unsaved changes only in add mode
                                if (!isEditMode) {
                                  bool hasUnsavedChanges =
                                      enumValueList.isNotEmpty;

                                  if (hasUnsavedChanges) {
                                    bool shouldClose =
                                        await _showUnsavedChangesWarning(
                                            context);
                                    if (shouldClose) {
                                      Navigator.of(context).pop();
                                    }
                                  } else {
                                    Navigator.of(context).pop();
                                  }
                                } else {
                                  isEditMode = false;
                                  editIndex = -1;
                                  attribute = null;
                                  enums = [
                                    EnumSpecial(1, TextEditingController()),
                                  ];
                                  childSetState(() {});
                                }
                              },
                              style: TextButton.styleFrom(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 24, vertical: 12),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8),
                                  side: BorderSide(color: Colors.grey[300]!),
                                ),
                              ),
                              child: Text(
                                'Cancel',
                                style: FontManager.getCustomStyle(
                                  fontSize:
                                      ResponsiveFontSizes.bodyMedium(context),
                                  fontWeight: FontWeight.w500,
                                  fontFamily: FontManager.fontFamilyTiemposText,
                                  color: Colors.grey[700],
                                ),
                              ),
                            ),
                            const SizedBox(width: 16),
                            ElevatedButton(
                              onPressed: () async {
                                bool pop = true;
                                if (!isEditMode) {
                                  pop = await _validateEntity(context,
                                      entityProvider.currentEntity, title,
                                      enumValueData: enumValueList);
                                }
                                if (pop && !isEditMode) {
                                  entityProvider.currentEntity?.enumValues
                                      ?.addAll(enumValueList);

                                  Navigator.of(context).pop();
                                  entityProvider
                                      .updateEntityEnumExpansion(false);
                                }
                                if (isEditMode && editIndex != -1) {
                                  final updatedEnum = EnumValue(
                                      entityAttribute: attribute,
                                      value: enums
                                          .map(
                                            (e) => e.textController.text,
                                          )
                                          .toList()
                                          .join(","));
                                  attribute = null;
                                  enums = [
                                    EnumSpecial(1, TextEditingController()),
                                  ];
                                  isEditMode = false;

                                  childSetState(() {
                                    enumValueList[editIndex] = updatedEnum;
                                  });
                                }
                              },
                              style: ElevatedButton.styleFrom(
                                backgroundColor: const Color(0xFF0058FF),
                                foregroundColor: Colors.white,
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 24, vertical: 12),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                elevation: 0,
                              ),
                              child: Text(
                                isEditMode ? 'Update' : 'Validate',
                                style: FontManager.getCustomStyle(
                                  fontSize:
                                      ResponsiveFontSizes.bodyMedium(context),
                                  fontWeight: FontWeight.w500,
                                  fontFamily: FontManager.fontFamilyTiemposText,
                                  color: Colors.white,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildEditableFormField(
      BuildContext context, String label, TextEditingController controller,
      {bool isEnabled = true,
      bool requireLabel = true,
      Function(String?)? onChanged}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (label.isNotEmpty)
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label + (requireLabel ? "*" : ""),
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.titleSmall(context),
                  fontWeight: FontWeight.w500,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.black,
                ),
              ),
              const SizedBox(height: 8),
            ],
          ),
        TextFormField(
          enabled: isEnabled,
          controller: controller,
          onChanged: onChanged,
          decoration: InputDecoration(
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Color(0xFF0058FF)),
            ),
            contentPadding: !requireLabel
                ? const EdgeInsets.symmetric(horizontal: 4, vertical: 4)
                : const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            filled: true,
            fillColor: Colors.white,
          ),
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.bodyMedium(context),
            fontWeight: FontWeight.w400,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.black,
          ),
        ),
      ],
    );
  }

  Widget _buildEditableDropdown(
    BuildContext context,
    String label,
    Function(dynamic) onChanged,
    List list,
    dynamic value, {
    bool requireLabel = true,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              label + (requireLabel ? "*" : ""),
              style: FontManager.getCustomStyle(
                fontSize: ResponsiveFontSizes.titleSmall(context),
                fontWeight: FontWeight.w500,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
              ),
            ),
            const SizedBox(height: 8),
          ],
        ),
        CustomDropdownWidget(
            label: label, list: list, onChanged: onChanged, value: value)
      ],
    );
  }

  /// Build key type button (Select One, Primary key, Foreign key)
  Widget _buildKeyTypeButton(BuildContext context, String label,
      bool isSelected, VoidCallback? onTap) {
    return MouseRegion(
      cursor: SystemMouseCursors.click,
      child: InkWell(
        onTap: onTap,
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            border: Border.all(
              color: isSelected ? const Color(0xFF0058FF) : Colors.grey[300]!,
            ),
            borderRadius: BorderRadius.circular(4),
            color: isSelected
                ? const Color(0xFF0058FF).withValues(alpha: 0.1)
                : Colors.white,
          ),
          child: Text(
            label,
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.bodySmall(context),
              fontWeight: FontWeight.w400,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: isSelected ? const Color(0xFF0058FF) : Colors.grey[600],
            ),
          ),
        ),
      ),
    );
  }

  /// Show unsaved changes warning dialog
  Future<bool> _showUnsavedChangesWarning(BuildContext context) async {
    return await showDialog<bool>(
          context: context,
          barrierDismissible: false,
          builder: (BuildContext context) {
            return AlertDialog(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              title: Text(
                'Unsaved Changes',
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.titleMedium(context),
                  fontWeight: FontWeight.w600,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.black,
                ),
              ),
              content: Text(
                'You have unsaved changes. If you close this dialog, your unsaved progress will be lost. Do you want to continue?',
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.bodyMedium(context),
                  fontWeight: FontWeight.w400,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.black87,
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(false),
                  child: Text(
                    'Cancel',
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.bodyMedium(context),
                      fontWeight: FontWeight.w500,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: Colors.grey[600],
                    ),
                  ),
                ),
                ElevatedButton(
                  onPressed: () => Navigator.of(context).pop(true),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red[600],
                    foregroundColor: Colors.white,
                  ),
                  child: Text(
                    'Discard Changes',
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.bodyMedium(context),
                      fontWeight: FontWeight.w500,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: Colors.white,
                    ),
                  ),
                ),
              ],
            );
          },
        ) ??
        false;
  }

  Future<bool> _showAlreadyExistWarning(BuildContext context) async {
    return await showDialog<bool>(
          context: context,
          barrierDismissible: false,
          builder: (BuildContext context) {
            return AlertDialog(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              title: Text(
                'Entity already exist',
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.titleMedium(context),
                  fontWeight: FontWeight.w600,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.black,
                ),
              ),
              content: Text(
                'Entity already exists, do you want to edit the same entity?',
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.bodyMedium(context),
                  fontWeight: FontWeight.w400,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.black87,
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(false),
                  child: Text(
                    'Cancel',
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.bodyMedium(context),
                      fontWeight: FontWeight.w500,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: Colors.grey[600],
                    ),
                  ),
                ),
                ElevatedButton(
                  onPressed: () => Navigator.of(context).pop(true),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Color(0xFF0058FF),
                    foregroundColor: Colors.white,
                  ),
                  child: Text(
                    'Proceed',
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.bodyMedium(context),
                      fontWeight: FontWeight.w500,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: Colors.white,
                    ),
                  ),
                ),
              ],
            );
          },
        ) ??
        false;
  }

  Future<bool> _validateEntity(
    BuildContext context,
    ObjectCreationModel? objectData,
    String title, {
    List<ObjectAttribute> attributeData = const [],
    List<EntityRelationship> relationshipData = const [],
    List<BusinessRule> businessRuleData = const [],
    List<EnumValue> enumValueData = const [],
    List<SecurityClassification> securityClassificationData = const [],
    List<SystemPermission> systemPermissionData = const [],
    bool isEditMode = false,
  }) async {
    try {
      final entityProvider =
          Provider.of<CreateEntityProvider>(context, listen: false);
      final provider =
          Provider.of<ObjectCreationProvider>(context, listen: false);
      bool showAlert = true;
      String errorString = '';

      switch (title) {
        case 'Object Details':
          errorString = validateObjectDetails(objectData);
          if (errorString.isNotEmpty) {
          } else {
            var temp = ObjectCreationModel(
              tenant: objectData?.tenant,
              entityDeclaration: objectData?.entityDeclaration,
              id: objectData?.id,
              name: objectData?.name,
              displayName: objectData?.displayName,
              type: objectData?.type,
              description: objectData?.description,
              businessPurpose: objectData?.businessPurpose,
              businessDomain: objectData?.businessDomain,
              category: objectData?.category,
              archivalStrategy: objectData?.archivalStrategy,
              colorTheme: objectData?.colorTheme,
              icon: objectData?.icon,
              tags: objectData?.tags,
            );
            showAlert = await provider.parseValidateEntity(
              temp,
              entityProvider.currentEntity,
              isEditMode: isEditMode,
            );
          }
          break;
        case 'Attribute Details':
        case 'Attribute Configuration':
          List<ObjectAttribute> temp = List.from(objectData?.attributes ?? []);
          temp.addAll(attributeData);
          showAlert = await provider.parseValidateEntityAttributes(
            ObjectCreationModel(
                tenant: objectData?.tenant,
                entityDeclaration: objectData?.entityDeclaration,
                id: objectData?.id,
                name: objectData?.name,
                displayName: objectData?.displayName,
                type: objectData?.type,
                description: objectData?.description,
                businessPurpose: objectData?.businessPurpose,
                businessDomain: objectData?.businessDomain,
                category: objectData?.category,
                archivalStrategy: objectData?.archivalStrategy,
                colorTheme: objectData?.colorTheme,
                icon: objectData?.icon,
                tags: objectData?.tags,
                attributes: temp),
            entityProvider.currentEntity,
          );
          break;
        case 'Entity Relationships':
        case 'Add Entity Relationship':
          List<EntityRelationship> temp =
              List.from(objectData?.relationships ?? []);
          temp.addAll(relationshipData);
          showAlert = await provider.parseValidateEntityRelationships(
            ObjectCreationModel(
              tenant: objectData?.tenant,
              entityDeclaration: objectData?.entityDeclaration,
              id: objectData?.id,
              name: objectData?.name,
              displayName: objectData?.displayName,
              type: objectData?.type,
              description: objectData?.description,
              businessPurpose: objectData?.businessPurpose,
              businessDomain: objectData?.businessDomain,
              category: objectData?.category,
              archivalStrategy: objectData?.archivalStrategy,
              colorTheme: objectData?.colorTheme,
              icon: objectData?.icon,
              tags: objectData?.tags,
              relationships: temp,
            ),
            entityProvider.currentEntity,
          );
          break;
        case 'Attribute Business Rules':
        case 'Add Business Rule':
          List<BusinessRule> temp = List.from(objectData?.businessRules ?? []);
          temp.addAll(businessRuleData);
          showAlert = await provider.parseValidateEntityAttributeValidations(
            ObjectCreationModel(
                tenant: objectData?.tenant,
                entityDeclaration: objectData?.entityDeclaration,
                id: objectData?.id,
                name: objectData?.name,
                displayName: objectData?.displayName,
                type: objectData?.type,
                description: objectData?.description,
                businessPurpose: objectData?.businessPurpose,
                businessDomain: objectData?.businessDomain,
                category: objectData?.category,
                archivalStrategy: objectData?.archivalStrategy,
                colorTheme: objectData?.colorTheme,
                icon: objectData?.icon,
                tags: objectData?.tags,
                businessRules: temp),
            entityProvider.currentEntity,
          );
          break;
        case 'Enumerated Values':
        case 'Create Enumerated Values':
          List<EnumValue> temp = List.from(objectData?.enumValues ?? []);
          temp.addAll(enumValueData);
          showAlert = await provider.parseValidateEntityEnumValues(
            ObjectCreationModel(
              tenant: objectData?.tenant,
              entityDeclaration: objectData?.entityDeclaration,
              id: objectData?.id,
              name: objectData?.name,
              displayName: objectData?.displayName,
              type: objectData?.type,
              description: objectData?.description,
              businessPurpose: objectData?.businessPurpose,
              businessDomain: objectData?.businessDomain,
              category: objectData?.category,
              archivalStrategy: objectData?.archivalStrategy,
              colorTheme: objectData?.colorTheme,
              icon: objectData?.icon,
              tags: objectData?.tags,
              enumValues: temp,
            ),
            entityProvider.currentEntity,
          );
          break;
        case 'System Permissions':
        case 'Add System Permission':
          List<SystemPermission> temp =
              List.from(objectData?.systemPermissions ?? []);
          temp.addAll(systemPermissionData);
          showAlert = await provider.parseValidateEntitySystemPermissions(
            ObjectCreationModel(
                tenant: objectData?.tenant,
                entityDeclaration: objectData?.entityDeclaration,
                id: objectData?.id,
                name: objectData?.name,
                displayName: objectData?.displayName,
                type: objectData?.type,
                description: objectData?.description,
                businessPurpose: objectData?.businessPurpose,
                businessDomain: objectData?.businessDomain,
                category: objectData?.category,
                archivalStrategy: objectData?.archivalStrategy,
                colorTheme: objectData?.colorTheme,
                icon: objectData?.icon,
                tags: objectData?.tags,
                systemPermissions: temp),
            entityProvider.currentEntity,
          );
          break;
        case 'Security Classification':
        case 'Add Security Classification':
          List<SecurityClassification> temp =
              List.from(objectData?.securityClassification ?? []);
          temp.addAll(securityClassificationData);
          showAlert = await provider.parseValidateEntitySecurityProperties(
            ObjectCreationModel(
              tenant: objectData?.tenant,
              entityDeclaration: objectData?.entityDeclaration,
              id: objectData?.id,
              name: objectData?.name,
              displayName: objectData?.displayName,
              type: objectData?.type,
              description: objectData?.description,
              businessPurpose: objectData?.businessPurpose,
              businessDomain: objectData?.businessDomain,
              category: objectData?.category,
              archivalStrategy: objectData?.archivalStrategy,
              colorTheme: objectData?.colorTheme,
              icon: objectData?.icon,
              tags: objectData?.tags,
              securityClassification: temp,
            ),
            entityProvider.currentEntity,
          );
          break;
      }

      if (showAlert) {
        if (provider.parseValidationEntityModel != null &&
            provider.parseValidationEntityModel?.error != null &&
            (provider.parseValidationEntityModel!.error!
                    .contains("Item already exists in draft.") ||
                provider.parseValidationEntityModel!.error!
                    .contains("Item already exists in database"))) {
          final value = await _showAlreadyExistWarning(context);
          if (value) {
            var temp = ObjectCreationModel(
              tenant: objectData?.tenant,
              entityDeclaration: objectData?.entityDeclaration,
              id: objectData?.id,
              name: objectData?.name,
              displayName: objectData?.displayName,
              type: objectData?.type,
              description: objectData?.description,
              businessPurpose: objectData?.businessPurpose,
              businessDomain: objectData?.businessDomain,
              category: objectData?.category,
              archivalStrategy: objectData?.archivalStrategy,
              colorTheme: objectData?.colorTheme,
              icon: objectData?.icon,
              tags: objectData?.tags,
            );
            final value2 = await provider.parseValidateEntity(
                temp, entityProvider.currentEntity,
                isEditMode: true);

            if (!value2) {
              return true;
            } else {
              return false;
            }
          }
        } else {
          await showDialog(
            context: context,
            builder: (context) {
              return validationErrorDialog(context, title, errorString);
            },
          );
          return false;
        }
      } else {
        return true;
      }
    } catch (e) {
      rethrow;
    }
    return false;
  }

  String validateObjectDetails(ObjectCreationModel? objectData) {
    if (objectData?.name != null && objectData!.name!.isEmpty) {
      return "Entity name is mandatory";
    } else if (objectData?.displayName != null &&
        objectData!.displayName!.isEmpty) {
      return "Entity name is mandatory";
    } else if (objectData?.type != null && objectData!.type!.isEmpty) {
      return "Entity type is mandatory";
    }
    // else if (objectData?.icon != null && objectData!.icon!.isEmpty) {
    //   return "Icon is mandatory";
    // }
    //  else if (objectData?.description != null &&
    //     objectData!.description!.isEmpty) {
    //   return "Description is mandatory";
    // }
    return '';
  }
}

class EditableRow extends StatefulWidget {
  final List<String> tableHeaders;
  final VoidCallback onDelete;
  final List<Widget> editWidgets;
  final ScrollController? scrollController;
  final Future<void> Function() onUpdate;

  const EditableRow({
    super.key,
    required this.tableHeaders,
    required this.onDelete,
    required this.editWidgets,
    this.scrollController,
    required this.onUpdate,
  });

  @override
  State<EditableRow> createState() => _EditableRowState();
}

class _EditableRowState extends State<EditableRow> {
  Set<int> editIndex = {};

  @override
  Widget build(BuildContext context) {
    final isScrollable = widget.tableHeaders.length > 6;
    List<String> scrollableHeaders = isScrollable
        ? widget.tableHeaders.sublist(0, widget.tableHeaders.length - 1)
        : [];

    return isScrollable
        ? _buildScrollableEditableRow(scrollableHeaders)
        : _buildEditableRow();
  }

  Widget _buildEditableRow() {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: const Color(0xffFAFCFD),
        border: Border.all(color: const Color(0xffB9C1CE)),
      ),
      child: Row(
        children: widget.tableHeaders.asMap().entries.map((entry) {
          final index = entry.key;
          final e = entry.value;

          return Expanded(
            child: Center(
              child: Padding(
                padding: const EdgeInsets.symmetric(vertical: AppSpacing.xs),
                child: e == "DELETE"
                    ? Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          if (editIndex.isNotEmpty) ...[
                            InkWell(
                              onTap: () async {
                                await widget.onUpdate.call();
                                setState(() {
                                  editIndex.clear();
                                });
                              },
                              child: const Icon(Icons.check,
                                  size: 16, color: Color(0xFF28A745)), // Green
                            ),
                            const SizedBox(width: AppSpacing.xxs),
                            InkWell(
                              onTap: () {
                                setState(() {
                                  editIndex.clear();
                                });
                              },
                              child: const Icon(Icons.close,
                                  size: 16, color: Color(0xFFDC3545)), // Red
                            ),
                            const SizedBox(width: AppSpacing.xs),
                          ],
                          InkWell(
                            onTap: widget.onDelete,
                            child: const Icon(Icons.delete_outline,
                                size: 16, color: Colors.red),
                          ),
                        ],
                      )
                    : editIndex.contains(index)
                        ? Padding(
                            padding: const EdgeInsets.all(AppSpacing.xs),
                            child: widget.editWidgets[index],
                          )
                        : InkWell(
                            onTap: () {
                              setState(() {
                                editIndex.add(index);
                              });
                            },
                            child: Text(
                              e,
                              textAlign: TextAlign.center,
                              style: FontManager.getCustomStyle(
                                color: Colors.black,
                                fontFamily: FontManager.fontFamilyTiemposText,
                                fontWeight: FontWeight.w400,
                              ),
                            ),
                          ),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildScrollableEditableRow(List<String> headers) {
    return Container(
      decoration: BoxDecoration(
        color: const Color(0xffFAFCFD),
        border: Border.all(color: const Color(0xffB9C1CE)),
      ),
      child: Row(
        children: [
          Expanded(
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              controller: widget.scrollController,
              child: Row(
                children: headers.asMap().entries.map((entry) {
                  final index = entry.key;
                  final value = entry.value;

                  return Container(
                    width: 150,
                    alignment: Alignment.center,
                    padding:
                        const EdgeInsets.symmetric(vertical: AppSpacing.xs),
                    child: editIndex.contains(index)
                        ? GestureDetector(
                            onTap: () {},
                            child: widget.editWidgets[index],
                          )
                        : InkWell(
                            onTap: () {
                              setState(() {
                                editIndex.add(index);
                              });
                            },
                            child: Text(
                              value,
                              textAlign: TextAlign.center,
                              style: FontManager.getCustomStyle(
                                color: Colors.black,
                                fontFamily: FontManager.fontFamilyTiemposText,
                                fontWeight: FontWeight.w400,
                              ),
                            ),
                          ),
                  );
                }).toList(),
              ),
            ),
          ),
          // Frozen DELETE & action buttons
          Container(
            width: 100,
            alignment: Alignment.center,
            color: const Color(0xffFAFCFD),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                if (editIndex.isNotEmpty) ...[
                  InkWell(
                    onTap: () async {
                      await widget.onUpdate();
                      setState(() {
                        editIndex.clear();
                      });
                    },
                    child: const Icon(Icons.check,
                        size: 16, color: Color(0xFF28A745)), // Green
                  ),
                  const SizedBox(width: AppSpacing.xxs),
                  InkWell(
                    onTap: () {
                      setState(() {
                        editIndex.clear();
                      });
                    },
                    child: const Icon(Icons.close,
                        size: 16, color: Color(0xFFDC3545)), // Red
                  ),
                  const SizedBox(width: AppSpacing.xs),
                ],
                InkWell(
                  onTap: widget.onDelete,
                  child: const Icon(Icons.delete_outline,
                      size: 16, color: Colors.red),
                ),
              ],
            ),
          )
        ],
      ),
    );
  }
}
