import 'dart:convert';

import 'package:excel/excel.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:nsl/models/object_creation_model.dart';
import 'package:nsl/models/parse_validation_entity/get_entity_attributes_model.dart';
import 'package:nsl/models/parse_validation_entity/get_entity_enum_values_model.dart';
import 'package:nsl/models/parse_validation_entity/get_entity_relationship_model.dart';
import 'package:nsl/services/entity_parse_validation_service.dart';
import 'package:nsl/utils/logger.dart';

class CreateEntityProvider extends ChangeNotifier {
  final EntityParseValidationService entityParseValidationService =
      EntityParseValidationService();
  final entityNameController = TextEditingController();
  // TextEditingController(text: "test");
  final entityIconController = TextEditingController();
  // TextEditingController(text: "test");
  final entityDescriptionController = TextEditingController();
  // TextEditingController(text: "test");
  bool isEditMode = true;

  ObjectCreationModel? currentEntity = ObjectCreationModel(id: "");
  final ScrollController headerScrollController = ScrollController();
  final Set<ScrollController> allControllers = {};
  bool _isSyncingScroll = false;
  bool _isValidating = false;
  String? _validationError;
  static List entityTypes = [
    "Master",
    "Reference",
    "Transaction",
    "Configuration",
    "Intelligence",
    "Aggregate",
    "Contextual"
  ];
  List attributesDataTypes = [
    'string',
    'integer',
    'decimal',
    'boolean',
    'date',
    'datetime',
    'text',
    'Enum',
    'Object',
    'Array',
    'MultiValue'
  ];
  List attributesUnique = ['No', 'Yes'];
  List attributesRequired = ['No', 'Yes'];
  List relationshipTypes = [
    'one-to-one',
    'one-to-many',
    'many-to-one',
    'many-to-many'
  ];
  List onDeletes = [
    'Set Null',
    'Restrict',
    'Cascade',
  ];
  List onUpdates = [
    'Set Null',
    'Restrict',
    'Cascade',
  ];
  List foreignKeyTypes = ['Nullable', 'Non-Nullable'];
  String selectedEntityType = entityTypes[0];

  String? get validationError => _validationError;
  bool get isValidating => _isValidating;

  /// Validates the solution and moves to next step
  Future<String?> validateSolution({required bool validate}) async {
    if (entityNameController.text.trim().isEmpty) {
      return "Cannot create entity without name";
    }
    // if (entityIconController.text.trim().isEmpty) {
    //   return "Cannot create entity without icon";
    // }
    // if (entityDescriptionController.text.trim().isEmpty) {
    //   return "Cannot create entity without description";
    // }

    _setValidating(true);
    _setValidationError(null);

    try {
      Logger.info('Validating entity: ${entityNameController.text}');
      if (validate) {
        currentEntity = ObjectCreationModel(
          id: currentEntity?.id,
          name: entityNameController.text,
          displayName: entityNameController.text,
          icon: entityIconController.text,
          type: selectedEntityType,
          description: entityDescriptionController.text,
          attributes: currentEntity?.attributes ?? [],
          relationships: currentEntity?.relationships ?? [],
          enumValues: currentEntity?.enumValues ?? [],
        );
        isEditMode = false;
      }

      Logger.info('CreateEntityProvider: Entity validated successfully');
    } catch (e) {
      Logger.error('CreateEntityProvider: Validation error - $e');
    } finally {
      _setValidating(false);
    }
    return null;
  }

  void updateEntityValue(value) {
    selectedEntityType = value;
    if (currentEntity != null) {
      currentEntity?.type = value;
    }
    notifyListeners();
  }

  /// Private helper methods
  void _setValidating(bool validating) {
    if (_isValidating != validating) {
      _isValidating = validating;
      notifyListeners();
    }
  }

  void _setValidationError(String? error) {
    if (_validationError != error) {
      _validationError = error;
      notifyListeners();
    }
  }

  void updateEntityAttributeExpansion(bool? value) {
    currentEntity?.attributesExpanded = !(value ?? false);
    notifyListeners();
  }

  void updateEntityRelationShipExpansion(bool? value) {
    currentEntity?.relationshipsExpanded = !(value ?? false);
    notifyListeners();
  }

  void updateEntityEnumExpansion(bool? value) {
    currentEntity?.enumValuesExpanded = !(value ?? false);
    notifyListeners();
  }

  void updateEditMode(value) {
    isEditMode = value;
    notifyListeners();
  }

  update() {
    notifyListeners();
  }

  resetObject() {
    currentEntity = ObjectCreationModel(id: "");
    isEditMode = true;
    notifyListeners();
  }

  Future<List<String>> pickAndReadFileLines() async {
    final result = await FilePicker.platform.pickFiles(
      type: FileType.custom,
      allowedExtensions: ['txt', 'csv', 'xlsx'],
      withData: true, // needed for web
    );

    if (result == null) return [];

    final file = result.files.single;
    final ext = file.extension?.toLowerCase();
    final bytes = file.bytes;

    if (bytes == null) return [];

    if (ext == 'txt' || ext == 'csv') {
      return utf8.decode(bytes).split(RegExp(r'\r?\n'));
    }

    if (ext == 'xlsx') {
      final excel = Excel.decodeBytes(bytes);
      final List<String> lines = [];
      for (final sheet in excel.tables.keys) {
        for (final row in excel.tables[sheet]!.rows) {
          final line =
              row.map((cell) => cell?.value.toString() ?? '').join(',');
          lines.add(line);
        }
      }
      return lines;
    }

    return [];
  }

  handleMyLibraryObject(ObjectCreationModel myLibObj) async {
    if (myLibObj.id != null) {
      myLibObj.attributes = [];
      myLibObj.relationships = [];
      myLibObj.enumValues = [];
      final entity =
          await entityParseValidationService.getEntity(entityId: myLibObj.id);
      final entityAttributes = await entityParseValidationService
          .getEntityAttributes(entityId: myLibObj.id);
      final entityRelationships = await entityParseValidationService
          .getEntityRelationships(entityId: myLibObj.id);
      final entityEnumValues = await entityParseValidationService
          .getEntityEnumValues(entityId: myLibObj.id);
      if (entity != null) {
        if (entity.postgresRecord != null) {
          myLibObj.name = entity.postgresRecord?.name;
          myLibObj.archivalStrategy = entity.postgresRecord?.archivalStrategy;
          myLibObj.businessDomain = entity.postgresRecord?.businessDomain;
          myLibObj.category = entity.postgresRecord?.category;
          myLibObj.colorTheme = entity.postgresRecord?.colourTheme;
          myLibObj.description = entity.postgresRecord?.description;
          myLibObj.displayName = entity.postgresRecord?.displayName;
          myLibObj.icon = entity.postgresRecord?.icon;
          myLibObj.tags = entity.postgresRecord?.tags;
          myLibObj.type = entity.postgresRecord?.type;
        } else if (entity.mongoDraft != null) {
          myLibObj.name = entity.mongoDraft?.name;
          myLibObj.archivalStrategy = entity.mongoDraft?.archivalStrategy;
          myLibObj.businessDomain = entity.mongoDraft?.businessDomain;
          myLibObj.category = entity.mongoDraft?.category;
          myLibObj.colorTheme = entity.mongoDraft?.colourTheme;
          myLibObj.description = entity.mongoDraft?.description;
          myLibObj.displayName = entity.mongoDraft?.displayName;
          myLibObj.icon = entity.mongoDraft?.icon;
          myLibObj.tags = entity.mongoDraft?.tags;
          myLibObj.type = entity.mongoDraft?.type;
        }
      }
      if (entityAttributes != null) {
        List<MongoDraftAttribute> temp = [];
        if (entityAttributes.postgresAttributes != null &&
            entityAttributes.postgresAttributes!.isNotEmpty) {
          temp = entityAttributes.postgresAttributes!;
        } else if (entityAttributes.mongoDrafts != null &&
            entityAttributes.mongoDrafts!.isNotEmpty) {
          temp = entityAttributes.mongoDrafts!;
        }
        List<ObjectAttribute> attr = [];
        for (var element in temp) {
          attr.add(ObjectAttribute(
            name: element.name,
            displayName: element.displayName,
            dataType: element.datatype,
            defaultType: element.defaultType,
            description: element.description,
            defaultValue: element.defaultType,
            helperText: element.helperText,
            isForeignKey: element.isForeignKey ?? false,
            isPrimaryKey: element.isPrimaryKey ?? false,
            required: element.isRequired,
            unique: element.isUnique,
          ));
        }
        myLibObj.attributes?.addAll(attr);
        if (myLibObj.attributes?.isNotEmpty ?? false) {
          myLibObj.isEntityAttributesValidatedSaved = true;
        }
      }
      if (entityRelationships != null) {
        List<MongoDraftRelationship> temp = [];
        if (entityRelationships.postgresRelationships != null &&
            entityRelationships.postgresRelationships!.isNotEmpty) {
          temp = entityRelationships.postgresRelationships ?? [];
        } else if (entityRelationships.mongoDrafts != null &&
            entityRelationships.mongoDrafts!.isNotEmpty) {
          temp = entityRelationships.mongoDrafts ?? [];
        }
        List<EntityRelationship> rel = [];
        for (var element in temp) {
          rel.add(EntityRelationship(
            primaryEntity: element.sourceEntityId,
            primaryKey: element.sourceAttributeName,
            foreignKey: element.targetEntityName,
            description: element.description,
            foreignKeyType: element.foreignKeyType,
            onDelete: element.onDelete,
            onUpdate: element.onUpdate,
            relationshipType: element.relationshipType,
            relatedEntity: element.targetEntityId,
          ));
        }
        myLibObj.relationships?.addAll(rel);
        if (myLibObj.relationships?.isNotEmpty ?? false) {
          myLibObj.isEntityRelationshipsValidatedSaved = true;
        }
      }

      if (entityEnumValues != null) {
        List<MongoDraftEnum> temp = [];
        if (entityEnumValues.postgresEnumValues != null &&
            entityEnumValues.postgresEnumValues!.isNotEmpty) {
          temp = entityEnumValues.postgresEnumValues ?? [];
        } else if (entityEnumValues.mongoDrafts != null &&
            entityEnumValues.mongoDrafts!.isNotEmpty) {
          temp = entityEnumValues.mongoDrafts ?? [];
        }
        List<EnumValue> enu = [];
        for (var element in temp) {
          enu.add(EnumValue(
              active: element.isActive,
              description: element.description,
              display: element.displayName,
              entityAttribute: element.attributeName,
              enumName: element.displayName,
              sortOrder: element.sortOrder,
              value: element.value));
        }
        myLibObj.enumValues?.addAll(enu);
        if (myLibObj.enumValues?.isNotEmpty ?? false) {
          myLibObj.isEntityEnumValuesValidatedSaved = true;
        }
      }

      currentEntity = myLibObj;
      isEditMode = false;
      setupScrollSync();
      notifyListeners();
    }
  }

  void setupScrollSync() {
    allControllers.clear();
    allControllers.add(headerScrollController);

    for (final relationship in currentEntity?.relationships ?? []) {
      allControllers.add(relationship.scrollController);
    }

    for (final controller in allControllers) {
      controller.addListener(() {
        if (_isSyncingScroll || !controller.hasClients) return;

        _isSyncingScroll = true;
        final offset = controller.offset;

        for (final other in allControllers) {
          if (other != controller && other.hasClients) {
            other.jumpTo(offset);
          }
        }

        _isSyncingScroll = false;
      });
    }
  }
}
